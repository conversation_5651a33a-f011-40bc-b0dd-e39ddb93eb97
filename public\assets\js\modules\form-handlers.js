/**
 * Module de gestion des formulaires
 * Gère la validation, la soumission et les interactions des formulaires
 */

class FormHandlers {
    constructor() {
        this.forms = new Map();
        this.validators = new Map();
        this.init();
    }

    init() {
        // Attendre que les autres modules soient chargés
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        // Initialiser les validateurs personnalisés
        this.setupCustomValidators();
        
        // Écouter les événements de changement de page
        document.addEventListener('pageChanged', (event) => {
            this.initializePage(event.detail.pageName);
        });
    }

    /**
     * Initialise les formulaires pour une page donnée
     * @param {string} pageName - Nom de la page
     */
    initializePage(pageName) {
        // Configuration des formulaires par page
        const pageConfigs = {
            locations: () => this.initializeLocationForms(),
            stops: () => this.initializeStopForms(),
            amenities: () => this.initializeAmenityForms(),
            seatPlans: () => this.initializeSeatPlanForms(),
            buses: () => this.initializeBusForms(),
            routes: () => this.initializeRouteForms(),
            trips: () => this.initializeTripForms(),
            pricing: () => this.initializePricingForms(),
            users: () => this.initializeUserForms()
        };

        const initializer = pageConfigs[pageName];
        if (initializer) {
            // Attendre un court délai pour que le DOM soit mis à jour
            setTimeout(initializer, 100);
        }
    }

    /**
     * Enregistre un formulaire avec sa configuration
     * @param {string} formId - ID du formulaire
     * @param {Object} config - Configuration du formulaire
     */
    registerForm(formId, config) {
        this.forms.set(formId, {
            ...config,
            element: null,
            isInitialized: false
        });
    }

    /**
     * Initialise un formulaire enregistré
     * @param {string} formId - ID du formulaire
     */
    initializeForm(formId) {
        const config = this.forms.get(formId);
        if (!config || config.isInitialized) return;

        const formElement = document.getElementById(formId);
        if (!formElement) return;

        config.element = formElement;
        config.isInitialized = true;

        // Ajouter les écouteurs d'événements
        this.attachFormEvents(formId, config);

        // Initialiser la validation en temps réel
        if (config.realTimeValidation !== false) {
            this.setupRealTimeValidation(formId);
        }

        // Charger les données initiales si nécessaire
        if (config.loadInitialData) {
            config.loadInitialData();
        }
    }

    /**
     * Attache les événements à un formulaire
     * @param {string} formId - ID du formulaire
     * @param {Object} config - Configuration du formulaire
     */
    attachFormEvents(formId, config) {
        const form = config.element;
        
        // Événement de soumission
        form.addEventListener('submit', async (event) => {
            event.preventDefault();
            await this.handleFormSubmit(formId, config);
        });

        // Événements personnalisés
        if (config.events) {
            Object.keys(config.events).forEach(eventType => {
                form.addEventListener(eventType, config.events[eventType]);
            });
        }
    }

    /**
     * Gère la soumission d'un formulaire
     * @param {string} formId - ID du formulaire
     * @param {Object} config - Configuration du formulaire
     */
    async handleFormSubmit(formId, config) {
        try {
            // Validation
            if (!this.validateForm(formId, config)) {
                return;
            }

            // Afficher l'indicateur de chargement
            this.setFormLoading(formId, true);

            // Récupérer les données
            const formData = this.getFormData(formId);

            // Traitement personnalisé des données
            let processedData = formData;
            if (config.processData) {
                processedData = await config.processData(formData);
            }

            // Soumission
            let result;
            if (config.submitHandler) {
                result = await config.submitHandler(processedData);
            } else if (config.apiEndpoint) {
                const method = config.method || 'POST';
                result = await window.ApiClient.request(method, config.apiEndpoint, processedData);
            }

            // Traitement du succès
            if (config.onSuccess) {
                config.onSuccess(result, formData);
            } else {
                this.handleFormSuccess(formId, result);
            }

        } catch (error) {
            console.error(`Erreur lors de la soumission du formulaire ${formId}:`, error);
            
            if (config.onError) {
                config.onError(error);
            } else {
                this.handleFormError(formId, error);
            }
        } finally {
            this.setFormLoading(formId, false);
        }
    }

    /**
     * Valide un formulaire
     * @param {string} formId - ID du formulaire
     * @param {Object} config - Configuration du formulaire
     * @returns {boolean} True si valide
     */
    validateForm(formId, config) {
        const form = document.getElementById(formId);
        if (!form) return false;

        let isValid = true;

        // Validation HTML5 native
        if (!form.checkValidity()) {
            isValid = false;
            form.classList.add('was-validated');
        }

        // Validations personnalisées
        if (config.customValidations) {
            const customErrors = config.customValidations(this.getFormData(formId));
            if (customErrors && Object.keys(customErrors).length > 0) {
                isValid = false;
                this.displayValidationErrors(formId, customErrors);
            }
        }

        return isValid;
    }

    /**
     * Affiche les erreurs de validation
     * @param {string} formId - ID du formulaire
     * @param {Object} errors - Erreurs de validation
     */
    displayValidationErrors(formId, errors) {
        Object.keys(errors).forEach(fieldName => {
            const field = document.querySelector(`#${formId} [name="${fieldName}"]`);
            if (field) {
                field.classList.add('is-invalid');
                const feedback = field.parentNode.querySelector('.invalid-feedback');
                if (feedback) {
                    feedback.textContent = errors[fieldName];
                }
            }
        });
    }

    /**
     * Configure la validation en temps réel
     * @param {string} formId - ID du formulaire
     */
    setupRealTimeValidation(formId) {
        const form = document.getElementById(formId);
        if (!form) return;

        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });

            input.addEventListener('input', () => {
                if (input.classList.contains('is-invalid')) {
                    this.validateField(input);
                }
            });
        });
    }

    /**
     * Valide un champ individuel
     * @param {HTMLElement} field - Élément du champ
     */
    validateField(field) {
        if (field.checkValidity()) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
            
            const feedback = field.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.textContent = field.validationMessage;
            }
        }
    }

    /**
     * Définit l'état de chargement d'un formulaire
     * @param {string} formId - ID du formulaire
     * @param {boolean} loading - État de chargement
     */
    setFormLoading(formId, loading) {
        const form = document.getElementById(formId);
        if (!form) return;

        const submitButton = form.querySelector('button[type="submit"]');
        const inputs = form.querySelectorAll('input, select, textarea, button');

        if (loading) {
            inputs.forEach(input => input.disabled = true);
            if (submitButton) {
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Traitement...';
            }
        } else {
            inputs.forEach(input => input.disabled = false);
            if (submitButton) {
                submitButton.innerHTML = submitButton.dataset.originalText || 'Enregistrer';
            }
        }
    }

    /**
     * Gère le succès d'une soumission
     * @param {string} formId - ID du formulaire
     * @param {*} result - Résultat de la soumission
     */
    handleFormSuccess(formId, result) {
        if (window.UIComponents) {
            window.UIComponents.showSuccess('Opération réussie !');
        }

        // Fermer la modal si le formulaire est dans une modal
        const modal = document.querySelector(`#${formId}`).closest('.modal');
        if (modal) {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        }

        // Recharger les données de la page
        if (window.DataManager && window.pageLoader) {
            const currentPage = window.pageLoader.getCurrentPage();
            window.DataManager.invalidateCache(currentPage);
            window.DataManager.loadPageData(currentPage);
        }
    }

    /**
     * Gère les erreurs de soumission
     * @param {string} formId - ID du formulaire
     * @param {Error} error - Erreur survenue
     */
    handleFormError(formId, error) {
        if (window.UIComponents) {
            window.UIComponents.showError(`Erreur: ${error.message}`);
        }
    }

    /**
     * Récupère les données d'un formulaire
     * @param {string} formId - ID du formulaire
     * @returns {Object} Données du formulaire
     */
    getFormData(formId) {
        const form = document.getElementById(formId);
        if (!form) return {};

        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            // Gérer les cases à cocher multiples
            if (data[key]) {
                if (Array.isArray(data[key])) {
                    data[key].push(value);
                } else {
                    data[key] = [data[key], value];
                }
            } else {
                data[key] = value;
            }
        }
        
        return data;
    }

    /**
     * Remplit un formulaire avec des données
     * @param {string} formId - ID du formulaire
     * @param {Object} data - Données à injecter
     */
    populateForm(formId, data) {
        const form = document.getElementById(formId);
        if (!form) return;

        Object.keys(data).forEach(key => {
            const field = form.querySelector(`[name="${key}"]`);
            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = !!data[key];
                } else if (field.type === 'radio') {
                    if (field.value === data[key]) {
                        field.checked = true;
                    }
                } else {
                    field.value = data[key] || '';
                }
            }
        });
    }

    /**
     * Réinitialise un formulaire
     * @param {string} formId - ID du formulaire
     */
    resetForm(formId) {
        const form = document.getElementById(formId);
        if (!form) return;

        form.reset();
        form.classList.remove('was-validated');
        form.querySelectorAll('.is-valid, .is-invalid').forEach(field => {
            field.classList.remove('is-valid', 'is-invalid');
        });
    }

    /**
     * Configure les validateurs personnalisés
     */
    setupCustomValidators() {
        // Validateur d'email
        this.validators.set('email', (value) => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(value) || 'Format d\'email invalide';
        });

        // Validateur de téléphone
        this.validators.set('phone', (value) => {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
            return phoneRegex.test(value) || 'Format de téléphone invalide';
        });

        // Validateur de mot de passe
        this.validators.set('password', (value) => {
            if (value.length < 8) return 'Le mot de passe doit contenir au moins 8 caractères';
            if (!/[A-Z]/.test(value)) return 'Le mot de passe doit contenir au moins une majuscule';
            if (!/[a-z]/.test(value)) return 'Le mot de passe doit contenir au moins une minuscule';
            if (!/[0-9]/.test(value)) return 'Le mot de passe doit contenir au moins un chiffre';
            return true;
        });
    }

    /**
     * Initialise les formulaires spécifiques à chaque page
     */
    initializeLocationForms() {
        this.registerForm('locationForm', {
            apiEndpoint: '/operator/locations',
            customValidations: (data) => {
                const errors = {};
                if (!data.name || data.name.trim().length < 2) {
                    errors.name = 'Le nom doit contenir au moins 2 caractères';
                }
                if (!data.latitude || isNaN(data.latitude)) {
                    errors.latitude = 'Latitude invalide';
                }
                if (!data.longitude || isNaN(data.longitude)) {
                    errors.longitude = 'Longitude invalide';
                }
                return errors;
            }
        });
        this.initializeForm('locationForm');
    }

    initializeStopForms() {
        this.registerForm('stopForm', {
            apiEndpoint: '/operator/stops',
            loadInitialData: async () => {
                // Charger les lieux pour le select
                if (window.DataManager) {
                    await window.DataManager.loadLocations();
                }
            }
        });
        this.initializeForm('stopForm');
    }

    initializeAmenityForms() {
        this.registerForm('amenityForm', {
            apiEndpoint: '/operator/amenities'
        });
        this.initializeForm('amenityForm');
    }

    initializeSeatPlanForms() {
        this.registerForm('seatPlanForm', {
            apiEndpoint: '/operator/seat-plans'
        });
        this.initializeForm('seatPlanForm');
    }

    initializeBusForms() {
        this.registerForm('busForm', {
            apiEndpoint: '/operator/buses',
            loadInitialData: async () => {
                // Charger les plans de sièges et commodités
                if (window.DataManager) {
                    await Promise.all([
                        window.DataManager.loadSeatPlans(),
                        window.DataManager.loadAmenities()
                    ]);
                }
            }
        });
        this.initializeForm('busForm');
    }

    initializeRouteForms() {
        this.registerForm('routeForm', {
            apiEndpoint: '/operator/routes',
            loadInitialData: async () => {
                // Charger les arrêts
                if (window.DataManager) {
                    await window.DataManager.loadStops();
                }
            }
        });
        this.initializeForm('routeForm');
    }

    initializeTripForms() {
        this.registerForm('tripForm', {
            apiEndpoint: '/operator/trips',
            loadInitialData: async () => {
                // Charger les itinéraires et bus
                if (window.DataManager) {
                    await Promise.all([
                        window.DataManager.loadRoutes(),
                        window.DataManager.loadBuses()
                    ]);
                }
            }
        });
        this.initializeForm('tripForm');
    }

    initializePricingForms() {
        this.registerForm('pricingForm', {
            apiEndpoint: '/operator/pricing',
            loadInitialData: async () => {
                // Charger les itinéraires
                if (window.DataManager) {
                    await window.DataManager.loadRoutes();
                }
            }
        });
        this.initializeForm('pricingForm');
    }

    initializeUserForms() {
        this.registerForm('userForm', {
            apiEndpoint: '/operator/users',
            customValidations: (data) => {
                const errors = {};
                if (data.email && !this.validators.get('email')(data.email)) {
                    errors.email = 'Format d\'email invalide';
                }
                if (data.phone && !this.validators.get('phone')(data.phone)) {
                    errors.phone = 'Format de téléphone invalide';
                }
                return errors;
            }
        });
        this.initializeForm('userForm');
    }
}

// Créer une instance globale du gestionnaire de formulaires
window.FormHandlers = new FormHandlers();
