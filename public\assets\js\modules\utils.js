/**
 * Module utilitaires
 * Fournit des fonctions utilitaires réutilisables
 */

class Utils {
    constructor() {
        this.init();
    }

    init() {
        // Configuration des formats de date/heure
        this.dateFormats = {
            short: { day: '2-digit', month: '2-digit', year: 'numeric' },
            long: { weekday: 'long', day: '2-digit', month: 'long', year: 'numeric' },
            time: { hour: '2-digit', minute: '2-digit' },
            datetime: { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit' }
        };

        this.locale = 'fr-FR';
        this.currency = 'XOF'; // Franc CFA
    }

    /**
     * Formate une date
     * @param {Date|string} date - Date à formater
     * @param {string} format - Format de date (short, long, time, datetime)
     * @returns {string} Date formatée
     */
    formatDate(date, format = 'short') {
        if (!date) return '';
        
        const dateObj = date instanceof Date ? date : new Date(date);
        if (isNaN(dateObj.getTime())) return '';

        const formatOptions = this.dateFormats[format] || this.dateFormats.short;
        return dateObj.toLocaleDateString(this.locale, formatOptions);
    }

    /**
     * Formate une heure
     * @param {Date|string} time - Heure à formater
     * @returns {string} Heure formatée
     */
    formatTime(time) {
        return this.formatDate(time, 'time');
    }

    /**
     * Formate une date et heure
     * @param {Date|string} datetime - Date/heure à formater
     * @returns {string} Date/heure formatée
     */
    formatDateTime(datetime) {
        return this.formatDate(datetime, 'datetime');
    }

    /**
     * Formate un montant en devise
     * @param {number} amount - Montant à formater
     * @param {string} currency - Code de devise (optionnel)
     * @returns {string} Montant formaté
     */
    formatCurrency(amount, currency = this.currency) {
        if (amount === null || amount === undefined || isNaN(amount)) return '0 FCFA';
        
        return new Intl.NumberFormat(this.locale, {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }

    /**
     * Formate un nombre
     * @param {number} number - Nombre à formater
     * @param {Object} options - Options de formatage
     * @returns {string} Nombre formaté
     */
    formatNumber(number, options = {}) {
        if (number === null || number === undefined || isNaN(number)) return '0';
        
        return new Intl.NumberFormat(this.locale, options).format(number);
    }

    /**
     * Calcule la durée entre deux dates
     * @param {Date|string} startDate - Date de début
     * @param {Date|string} endDate - Date de fin
     * @returns {Object} Durée calculée
     */
    calculateDuration(startDate, endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        
        if (isNaN(start.getTime()) || isNaN(end.getTime())) {
            return { hours: 0, minutes: 0, text: '0h 0min' };
        }

        const diffMs = end.getTime() - start.getTime();
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        const hours = Math.floor(diffMinutes / 60);
        const minutes = diffMinutes % 60;

        return {
            hours,
            minutes,
            totalMinutes: diffMinutes,
            text: `${hours}h ${minutes}min`
        };
    }

    /**
     * Génère un ID unique
     * @param {string} prefix - Préfixe optionnel
     * @returns {string} ID unique
     */
    generateId(prefix = '') {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 5);
        return prefix ? `${prefix}-${timestamp}-${random}` : `${timestamp}-${random}`;
    }

    /**
     * Débounce une fonction
     * @param {Function} func - Fonction à débouncer
     * @param {number} wait - Délai d'attente en millisecondes
     * @returns {Function} Fonction débouncée
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Throttle une fonction
     * @param {Function} func - Fonction à throttler
     * @param {number} limit - Limite en millisecondes
     * @returns {Function} Fonction throttlée
     */
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Valide un email
     * @param {string} email - Email à valider
     * @returns {boolean} True si valide
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Valide un numéro de téléphone
     * @param {string} phone - Téléphone à valider
     * @returns {boolean} True si valide
     */
    isValidPhone(phone) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
        return phoneRegex.test(phone);
    }

    /**
     * Nettoie une chaîne de caractères
     * @param {string} str - Chaîne à nettoyer
     * @returns {string} Chaîne nettoyée
     */
    sanitizeString(str) {
        if (typeof str !== 'string') return '';
        return str.trim().replace(/\s+/g, ' ');
    }

    /**
     * Capitalise la première lettre d'une chaîne
     * @param {string} str - Chaîne à capitaliser
     * @returns {string} Chaîne capitalisée
     */
    capitalize(str) {
        if (typeof str !== 'string' || str.length === 0) return '';
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    }

    /**
     * Convertit une chaîne en slug
     * @param {string} str - Chaîne à convertir
     * @returns {string} Slug généré
     */
    slugify(str) {
        if (typeof str !== 'string') return '';
        
        return str
            .toLowerCase()
            .trim()
            .replace(/[àáâãäå]/g, 'a')
            .replace(/[èéêë]/g, 'e')
            .replace(/[ìíîï]/g, 'i')
            .replace(/[òóôõö]/g, 'o')
            .replace(/[ùúûü]/g, 'u')
            .replace(/[ç]/g, 'c')
            .replace(/[ñ]/g, 'n')
            .replace(/[^a-z0-9 -]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }

    /**
     * Tronque un texte
     * @param {string} text - Texte à tronquer
     * @param {number} length - Longueur maximale
     * @param {string} suffix - Suffixe à ajouter
     * @returns {string} Texte tronqué
     */
    truncate(text, length = 100, suffix = '...') {
        if (typeof text !== 'string') return '';
        if (text.length <= length) return text;
        return text.substring(0, length).trim() + suffix;
    }

    /**
     * Copie du texte dans le presse-papiers
     * @param {string} text - Texte à copier
     * @returns {Promise<boolean>} True si succès
     */
    async copyToClipboard(text) {
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text);
                return true;
            } else {
                // Fallback pour les navigateurs plus anciens
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                const result = document.execCommand('copy');
                textArea.remove();
                return result;
            }
        } catch (error) {
            console.error('Erreur lors de la copie:', error);
            return false;
        }
    }

    /**
     * Télécharge un fichier
     * @param {string} url - URL du fichier
     * @param {string} filename - Nom du fichier
     */
    downloadFile(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    /**
     * Exporte des données en CSV
     * @param {Array} data - Données à exporter
     * @param {string} filename - Nom du fichier
     * @param {Array} headers - En-têtes des colonnes
     */
    exportToCSV(data, filename = 'export.csv', headers = null) {
        if (!Array.isArray(data) || data.length === 0) return;

        let csv = '';
        
        // Ajouter les en-têtes
        if (headers) {
            csv += headers.join(',') + '\n';
        } else if (data.length > 0) {
            csv += Object.keys(data[0]).join(',') + '\n';
        }

        // Ajouter les données
        data.forEach(row => {
            const values = Object.values(row).map(value => {
                // Échapper les guillemets et entourer de guillemets si nécessaire
                if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                    return '"' + value.replace(/"/g, '""') + '"';
                }
                return value;
            });
            csv += values.join(',') + '\n';
        });

        // Créer et télécharger le fichier
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        this.downloadFile(url, filename);
        URL.revokeObjectURL(url);
    }

    /**
     * Filtre un tableau d'objets
     * @param {Array} data - Données à filtrer
     * @param {string} searchTerm - Terme de recherche
     * @param {Array} searchFields - Champs à rechercher
     * @returns {Array} Données filtrées
     */
    filterData(data, searchTerm, searchFields = []) {
        if (!searchTerm || !Array.isArray(data)) return data;

        const term = searchTerm.toLowerCase();
        
        return data.filter(item => {
            if (searchFields.length === 0) {
                // Rechercher dans toutes les propriétés
                return Object.values(item).some(value => 
                    String(value).toLowerCase().includes(term)
                );
            } else {
                // Rechercher dans les champs spécifiés
                return searchFields.some(field => 
                    String(item[field] || '').toLowerCase().includes(term)
                );
            }
        });
    }

    /**
     * Trie un tableau d'objets
     * @param {Array} data - Données à trier
     * @param {string} field - Champ de tri
     * @param {string} direction - Direction (asc/desc)
     * @returns {Array} Données triées
     */
    sortData(data, field, direction = 'asc') {
        if (!Array.isArray(data)) return data;

        return [...data].sort((a, b) => {
            let aVal = a[field];
            let bVal = b[field];

            // Gérer les valeurs nulles/undefined
            if (aVal == null) aVal = '';
            if (bVal == null) bVal = '';

            // Gérer les nombres
            if (typeof aVal === 'number' && typeof bVal === 'number') {
                return direction === 'asc' ? aVal - bVal : bVal - aVal;
            }

            // Gérer les dates
            if (aVal instanceof Date && bVal instanceof Date) {
                return direction === 'asc' ? aVal - bVal : bVal - aVal;
            }

            // Gérer les chaînes
            const aStr = String(aVal).toLowerCase();
            const bStr = String(bVal).toLowerCase();
            
            if (direction === 'asc') {
                return aStr.localeCompare(bStr);
            } else {
                return bStr.localeCompare(aStr);
            }
        });
    }

    /**
     * Pagine un tableau de données
     * @param {Array} data - Données à paginer
     * @param {number} page - Numéro de page (1-based)
     * @param {number} pageSize - Taille de page
     * @returns {Object} Résultat de pagination
     */
    paginateData(data, page = 1, pageSize = 10) {
        if (!Array.isArray(data)) return { data: [], pagination: {} };

        const totalItems = data.length;
        const totalPages = Math.ceil(totalItems / pageSize);
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedData = data.slice(startIndex, endIndex);

        return {
            data: paginatedData,
            pagination: {
                currentPage: page,
                pageSize,
                totalItems,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1,
                startIndex: startIndex + 1,
                endIndex: Math.min(endIndex, totalItems)
            }
        };
    }

    /**
     * Stockage local sécurisé
     */
    storage = {
        set: (key, value) => {
            try {
                localStorage.setItem(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('Erreur lors de la sauvegarde:', error);
                return false;
            }
        },

        get: (key, defaultValue = null) => {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('Erreur lors de la lecture:', error);
                return defaultValue;
            }
        },

        remove: (key) => {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                console.error('Erreur lors de la suppression:', error);
                return false;
            }
        },

        clear: () => {
            try {
                localStorage.clear();
                return true;
            } catch (error) {
                console.error('Erreur lors du nettoyage:', error);
                return false;
            }
        }
    };
}

// Créer une instance globale des utilitaires
window.Utils = new Utils();
