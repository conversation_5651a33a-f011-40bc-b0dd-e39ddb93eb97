/**
 * Module des composants UI réutilisables
 * Fournit des fonctions pour créer et gérer les éléments d'interface
 */

class UIComponents {
    constructor() {
        this.alertContainer = null;
        this.init();
    }

    init() {
        // Attendre que le DOM soit chargé
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.alertContainer = document.getElementById('alertContainer');
    }

    /**
     * Affiche une alerte
     * @param {string} message - Message à afficher
     * @param {string} type - Type d'alerte (success, danger, warning, info)
     * @param {number} duration - Durée d'affichage en millisecondes (0 = permanent)
     */
    showAlert(message, type = 'info', duration = 5000) {
        if (!this.alertContainer) {
            console.warn('Container d\'alertes non trouvé');
            return;
        }

        const alertId = 'alert-' + Date.now();
        const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        this.alertContainer.insertAdjacentHTML('beforeend', alertHtml);

        // Auto-suppression après la durée spécifiée
        if (duration > 0) {
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    const bsAlert = new bootstrap.Alert(alertElement);
                    bsAlert.close();
                }
            }, duration);
        }
    }

    /**
     * Affiche une alerte de succès
     * @param {string} message - Message de succès
     * @param {number} duration - Durée d'affichage
     */
    showSuccess(message, duration = 3000) {
        this.showAlert(message, 'success', duration);
    }

    /**
     * Affiche une alerte d'erreur
     * @param {string} message - Message d'erreur
     * @param {number} duration - Durée d'affichage
     */
    showError(message, duration = 7000) {
        this.showAlert(message, 'danger', duration);
    }

    /**
     * Affiche une alerte d'avertissement
     * @param {string} message - Message d'avertissement
     * @param {number} duration - Durée d'affichage
     */
    showWarning(message, duration = 5000) {
        this.showAlert(message, 'warning', duration);
    }

    /**
     * Affiche une alerte d'information
     * @param {string} message - Message d'information
     * @param {number} duration - Durée d'affichage
     */
    showInfo(message, duration = 4000) {
        this.showAlert(message, 'info', duration);
    }

    /**
     * Crée un indicateur de chargement
     * @param {string} message - Message de chargement
     * @returns {string} HTML de l'indicateur
     */
    createLoadingIndicator(message = 'Chargement...') {
        return `
            <div class="d-flex justify-content-center align-items-center py-4">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2 text-muted">${message}</p>
                </div>
            </div>
        `;
    }

    /**
     * Crée un message d'état vide
     * @param {string} message - Message à afficher
     * @param {string} icon - Classe d'icône FontAwesome
     * @returns {string} HTML du message
     */
    createEmptyState(message = 'Aucune donnée disponible', icon = 'fas fa-inbox') {
        return `
            <div class="text-center py-5">
                <i class="${icon} fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">${message}</h5>
            </div>
        `;
    }

    /**
     * Crée un badge de statut
     * @param {string} status - Statut
     * @param {Object} statusConfig - Configuration des statuts
     * @returns {string} HTML du badge
     */
    createStatusBadge(status, statusConfig = {}) {
        const defaultConfig = {
            active: { class: 'bg-success', text: 'Actif' },
            inactive: { class: 'bg-secondary', text: 'Inactif' },
            pending: { class: 'bg-warning', text: 'En attente' },
            confirmed: { class: 'bg-success', text: 'Confirmé' },
            cancelled: { class: 'bg-danger', text: 'Annulé' },
            completed: { class: 'bg-info', text: 'Terminé' },
            ongoing: { class: 'bg-primary', text: 'En cours' },
            scheduled: { class: 'bg-secondary', text: 'Programmé' }
        };

        const config = { ...defaultConfig, ...statusConfig };
        const statusInfo = config[status] || { class: 'bg-secondary', text: status };

        return `<span class="badge ${statusInfo.class}">${statusInfo.text}</span>`;
    }

    /**
     * Crée un bouton d'action
     * @param {string} text - Texte du bouton
     * @param {string} icon - Classe d'icône
     * @param {string} variant - Variante Bootstrap (primary, secondary, etc.)
     * @param {string} onclick - Fonction à exécuter au clic
     * @param {Object} options - Options supplémentaires
     * @returns {string} HTML du bouton
     */
    createActionButton(text, icon, variant = 'primary', onclick = '', options = {}) {
        const size = options.size ? `btn-${options.size}` : '';
        const disabled = options.disabled ? 'disabled' : '';
        const classes = options.classes || '';
        
        return `
            <button type="button" 
                    class="btn btn-${variant} ${size} ${classes}" 
                    onclick="${onclick}"
                    ${disabled}>
                <i class="${icon} me-2"></i>${text}
            </button>
        `;
    }

    /**
     * Crée un groupe de boutons d'actions
     * @param {Array} actions - Tableau d'actions
     * @returns {string} HTML du groupe de boutons
     */
    createActionButtons(actions) {
        const buttons = actions.map(action => 
            this.createActionButton(
                action.text, 
                action.icon, 
                action.variant || 'outline-secondary',
                action.onclick,
                action.options || {}
            )
        ).join(' ');

        return `<div class="btn-group" role="group">${buttons}</div>`;
    }

    /**
     * Crée un tableau responsive
     * @param {Array} columns - Configuration des colonnes
     * @param {Array} data - Données du tableau
     * @param {Object} options - Options du tableau
     * @returns {string} HTML du tableau
     */
    createTable(columns, data, options = {}) {
        const tableClass = options.striped ? 'table-striped' : '';
        const responsive = options.responsive !== false;

        let html = responsive ? '<div class="table-responsive">' : '';
        html += `<table class="table ${tableClass}">`;

        // En-têtes
        html += '<thead><tr>';
        columns.forEach(col => {
            html += `<th scope="col">${col.title}</th>`;
        });
        html += '</tr></thead>';

        // Corps du tableau
        html += '<tbody>';
        if (data.length === 0) {
            html += `<tr><td colspan="${columns.length}" class="text-center text-muted py-4">Aucune donnée disponible</td></tr>`;
        } else {
            data.forEach(row => {
                html += '<tr>';
                columns.forEach(col => {
                    let cellValue = row[col.field] || '';
                    
                    // Appliquer le formateur si défini
                    if (col.formatter && typeof col.formatter === 'function') {
                        cellValue = col.formatter(cellValue, row);
                    }
                    
                    html += `<td>${cellValue}</td>`;
                });
                html += '</tr>';
            });
        }
        html += '</tbody></table>';

        if (responsive) {
            html += '</div>';
        }

        return html;
    }

    /**
     * Crée une grille de cartes
     * @param {Array} items - Éléments à afficher
     * @param {Function} cardRenderer - Fonction pour rendre chaque carte
     * @param {Object} options - Options de la grille
     * @returns {string} HTML de la grille
     */
    createCardGrid(items, cardRenderer, options = {}) {
        const colClass = options.colClass || 'col-md-6 col-lg-4';
        
        if (items.length === 0) {
            return this.createEmptyState(options.emptyMessage || 'Aucun élément à afficher');
        }

        let html = '<div class="row">';
        items.forEach(item => {
            html += `<div class="${colClass} mb-4">`;
            html += cardRenderer(item);
            html += '</div>';
        });
        html += '</div>';

        return html;
    }

    /**
     * Crée une modal Bootstrap
     * @param {string} id - ID de la modal
     * @param {string} title - Titre de la modal
     * @param {string} body - Contenu du corps
     * @param {Array} buttons - Boutons du footer
     * @param {Object} options - Options de la modal
     * @returns {string} HTML de la modal
     */
    createModal(id, title, body, buttons = [], options = {}) {
        const size = options.size ? `modal-${options.size}` : '';
        const centered = options.centered ? 'modal-dialog-centered' : '';
        
        let footerHtml = '';
        if (buttons.length > 0) {
            footerHtml = '<div class="modal-footer">';
            buttons.forEach(button => {
                footerHtml += this.createActionButton(
                    button.text,
                    button.icon || '',
                    button.variant || 'secondary',
                    button.onclick || '',
                    button.options || {}
                );
            });
            footerHtml += '</div>';
        }

        return `
            <div class="modal fade" id="${id}" tabindex="-1">
                <div class="modal-dialog ${size} ${centered}">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${body}
                        </div>
                        ${footerHtml}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Crée un formulaire avec validation
     * @param {Array} fields - Configuration des champs
     * @param {Object} options - Options du formulaire
     * @returns {string} HTML du formulaire
     */
    createForm(fields, options = {}) {
        const formId = options.id || 'dynamicForm';
        
        let html = `<form id="${formId}" novalidate>`;
        
        fields.forEach(field => {
            html += this.createFormField(field);
        });
        
        html += '</form>';
        return html;
    }

    /**
     * Crée un champ de formulaire
     * @param {Object} field - Configuration du champ
     * @returns {string} HTML du champ
     */
    createFormField(field) {
        const required = field.required ? 'required' : '';
        const value = field.value || '';
        
        let html = `<div class="mb-3">`;
        
        if (field.label) {
            html += `<label for="${field.id}" class="form-label">${field.label}${field.required ? ' *' : ''}</label>`;
        }
        
        switch (field.type) {
            case 'select':
                html += `<select class="form-select" id="${field.id}" name="${field.name}" ${required}>`;
                if (field.placeholder) {
                    html += `<option value="">${field.placeholder}</option>`;
                }
                if (field.options) {
                    field.options.forEach(option => {
                        const selected = option.value === value ? 'selected' : '';
                        html += `<option value="${option.value}" ${selected}>${option.text}</option>`;
                    });
                }
                html += '</select>';
                break;
                
            case 'textarea':
                html += `<textarea class="form-control" id="${field.id}" name="${field.name}" 
                         rows="${field.rows || 3}" ${required}>${value}</textarea>`;
                break;
                
            default:
                html += `<input type="${field.type || 'text'}" class="form-control" 
                         id="${field.id}" name="${field.name}" value="${value}" 
                         placeholder="${field.placeholder || ''}" ${required}>`;
        }
        
        if (field.help) {
            html += `<div class="form-text">${field.help}</div>`;
        }
        
        html += `<div class="invalid-feedback"></div>`;
        html += '</div>';
        
        return html;
    }

    /**
     * Valide un formulaire
     * @param {string} formId - ID du formulaire
     * @returns {boolean} True si valide
     */
    validateForm(formId) {
        const form = document.getElementById(formId);
        if (!form) return false;

        let isValid = true;
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            if (!input.checkValidity()) {
                isValid = false;
                input.classList.add('is-invalid');
                
                const feedback = input.parentNode.querySelector('.invalid-feedback');
                if (feedback) {
                    feedback.textContent = input.validationMessage;
                }
            } else {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            }
        });

        return isValid;
    }

    /**
     * Récupère les données d'un formulaire
     * @param {string} formId - ID du formulaire
     * @returns {Object} Données du formulaire
     */
    getFormData(formId) {
        const form = document.getElementById(formId);
        if (!form) return {};

        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        return data;
    }

    /**
     * Remplit un formulaire avec des données
     * @param {string} formId - ID du formulaire
     * @param {Object} data - Données à injecter
     */
    populateForm(formId, data) {
        const form = document.getElementById(formId);
        if (!form) return;

        Object.keys(data).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = data[key] || '';
            }
        });
    }

    /**
     * Réinitialise un formulaire
     * @param {string} formId - ID du formulaire
     */
    resetForm(formId) {
        const form = document.getElementById(formId);
        if (!form) return;

        form.reset();
        form.querySelectorAll('.is-valid, .is-invalid').forEach(input => {
            input.classList.remove('is-valid', 'is-invalid');
        });
    }
}

// Créer une instance globale des composants UI
window.UIComponents = new UIComponents();
