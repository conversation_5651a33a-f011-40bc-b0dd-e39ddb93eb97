/**
 * Script principal du tableau de bord opérateur
 * Orchestre l'initialisation et la coordination des modules
 */

class OperatorDashboard {
    constructor() {
        this.currentSection = 'dashboard';
        this.isInitialized = false;
        this.modules = {};
        this.init();
    }

    init() {
        // Attendre que le DOM soit chargé
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        // Vérifier l'authentification
        if (!this.checkAuthentication()) {
            return;
        }

        // Attendre que tous les modules soient chargés
        this.waitForModules().then(() => {
            this.initializeModules();
            this.setupEventListeners();
            this.loadDefaultSection();
            this.isInitialized = true;
        });
    }

    /**
     * Vérifie l'authentification de l'utilisateur
     */
    checkAuthentication() {
        const token = localStorage.getItem('operator_token');
        if (!token) {
            window.location.href = '/operator/login.html';
            return false;
        }

        // Vérifier la validité du token si l'API client est disponible
        if (window.ApiClient && !window.ApiClient.isAuthenticated()) {
            window.location.href = '/operator/login.html';
            return false;
        }

        return true;
    }

    /**
     * Attend que tous les modules requis soient chargés
     */
    async waitForModules() {
        const requiredModules = [
            'pageLoader',
            'DataManager', 
            'ApiClient',
            'UIComponents',
            'FormHandlers',
            'Utils'
        ];

        const maxWait = 10000; // 10 secondes maximum
        const startTime = Date.now();

        while (Date.now() - startTime < maxWait) {
            const allLoaded = requiredModules.every(module => window[module]);
            if (allLoaded) {
                return;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.warn('Certains modules ne se sont pas chargés dans les temps');
    }

    /**
     * Initialise les modules
     */
    initializeModules() {
        // Stocker les références aux modules
        this.modules = {
            pageLoader: window.pageLoader,
            dataManager: window.DataManager,
            apiClient: window.ApiClient,
            uiComponents: window.UIComponents,
            formHandlers: window.FormHandlers,
            utils: window.Utils
        };

        // Configurer les modules si nécessaire
        this.configureModules();
    }

    /**
     * Configure les modules avec des paramètres spécifiques
     */
    configureModules() {
        // Configuration du gestionnaire de données
        if (this.modules.dataManager) {
            // Définir une durée de cache plus courte pour les données critiques
            this.modules.dataManager.defaultCacheDuration = 3 * 60 * 1000; // 3 minutes
        }

        // Configuration de l'API client
        if (this.modules.apiClient) {
            // Ajouter des intercepteurs si nécessaire
            this.setupApiInterceptors();
        }
    }

    /**
     * Configure les intercepteurs API
     */
    setupApiInterceptors() {
        // Cette méthode peut être étendue pour ajouter des intercepteurs
        // pour la gestion globale des erreurs, des tokens, etc.
    }

    /**
     * Configure les écouteurs d'événements
     */
    setupEventListeners() {
        // Écouter les changements de page
        document.addEventListener('pageChanged', (event) => {
            this.currentSection = event.detail.pageName;
            this.onPageChanged(event.detail);
        });

        // Écouter les événements de déconnexion
        document.addEventListener('click', (event) => {
            if (event.target.closest('[onclick*="logout"]')) {
                event.preventDefault();
                this.logout();
            }
        });

        // Écouter les erreurs globales
        window.addEventListener('error', (event) => {
            this.handleGlobalError(event);
        });

        // Écouter les erreurs de promesses non gérées
        window.addEventListener('unhandledrejection', (event) => {
            this.handleUnhandledRejection(event);
        });

        // Écouter les changements de connexion
        window.addEventListener('online', () => {
            this.handleConnectionChange(true);
        });

        window.addEventListener('offline', () => {
            this.handleConnectionChange(false);
        });
    }

    /**
     * Charge la section par défaut
     */
    loadDefaultSection() {
        // Charger le tableau de bord par défaut
        if (this.modules.pageLoader) {
            this.modules.pageLoader.loadPage('dashboard');
        }
    }

    /**
     * Gère les changements de page
     */
    onPageChanged(pageInfo) {
        // Mettre à jour l'URL sans recharger la page
        if (history.pushState) {
            const newUrl = `${window.location.pathname}#${pageInfo.pageName}`;
            history.pushState({ page: pageInfo.pageName }, '', newUrl);
        }

        // Déclencher des actions spécifiques selon la page
        this.handlePageSpecificActions(pageInfo.pageName);
    }

    /**
     * Gère les actions spécifiques à chaque page
     */
    handlePageSpecificActions(pageName) {
        switch (pageName) {
            case 'dashboard':
                this.setupDashboardRefresh();
                break;
            case 'validation':
                this.setupValidationShortcuts();
                break;
            // Ajouter d'autres cas selon les besoins
        }
    }

    /**
     * Configure l'actualisation automatique du tableau de bord
     */
    setupDashboardRefresh() {
        // Actualiser les données du tableau de bord toutes les 5 minutes
        if (this.dashboardRefreshInterval) {
            clearInterval(this.dashboardRefreshInterval);
        }

        this.dashboardRefreshInterval = setInterval(() => {
            if (this.currentSection === 'dashboard' && this.modules.dataManager) {
                this.modules.dataManager.loadDashboardData();
            }
        }, 5 * 60 * 1000); // 5 minutes
    }

    /**
     * Configure les raccourcis clavier pour la validation
     */
    setupValidationShortcuts() {
        // Ajouter des raccourcis clavier spécifiques à la validation
        const handleKeyPress = (event) => {
            if (event.ctrlKey && event.key === 'Enter') {
                // Ctrl+Enter pour valider rapidement
                if (window.validateTicket) {
                    window.validateTicket();
                }
            }
        };

        document.addEventListener('keydown', handleKeyPress);

        // Nettoyer l'écouteur quand on quitte la page
        document.addEventListener('pageChanged', () => {
            document.removeEventListener('keydown', handleKeyPress);
        }, { once: true });
    }

    /**
     * Gère les erreurs globales
     */
    handleGlobalError(event) {
        console.error('Erreur globale:', event.error);
        
        if (this.modules.uiComponents) {
            this.modules.uiComponents.showError('Une erreur inattendue s\'est produite');
        }
    }

    /**
     * Gère les rejets de promesses non gérés
     */
    handleUnhandledRejection(event) {
        console.error('Promesse rejetée non gérée:', event.reason);
        
        if (this.modules.uiComponents) {
            this.modules.uiComponents.showError('Erreur de communication avec le serveur');
        }
    }

    /**
     * Gère les changements de connexion
     */
    handleConnectionChange(isOnline) {
        if (this.modules.uiComponents) {
            if (isOnline) {
                this.modules.uiComponents.showSuccess('Connexion rétablie');
                // Recharger les données si nécessaire
                this.refreshCurrentPageData();
            } else {
                this.modules.uiComponents.showWarning('Connexion perdue - Mode hors ligne');
            }
        }
    }

    /**
     * Actualise les données de la page courante
     */
    refreshCurrentPageData() {
        if (this.modules.dataManager) {
            this.modules.dataManager.loadPageData(this.currentSection);
        }
    }

    /**
     * Déconnecte l'utilisateur
     */
    logout() {
        if (!confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
            return;
        }

        // Nettoyer les intervalles
        if (this.dashboardRefreshInterval) {
            clearInterval(this.dashboardRefreshInterval);
        }

        // Appeler l'API de déconnexion si disponible
        if (this.modules.apiClient) {
            this.modules.apiClient.logout().finally(() => {
                this.redirectToLogin();
            });
        } else {
            this.redirectToLogin();
        }
    }

    /**
     * Redirige vers la page de connexion
     */
    redirectToLogin() {
        // Nettoyer le stockage local
        localStorage.removeItem('operator_token');
        
        // Vider les caches
        if (this.modules.dataManager) {
            this.modules.dataManager.clearCache();
        }

        // Rediriger
        window.location.href = '/operator/login.html';
    }

    /**
     * Obtient l'état actuel du tableau de bord
     */
    getState() {
        return {
            currentSection: this.currentSection,
            isInitialized: this.isInitialized,
            modules: Object.keys(this.modules)
        };
    }
}

// Créer une instance globale du tableau de bord
window.operatorDashboard = new OperatorDashboard();

// Fonction globale pour la déconnexion (utilisée par les liens HTML)
window.logout = function() {
    if (window.operatorDashboard) {
        window.operatorDashboard.logout();
    }
};
