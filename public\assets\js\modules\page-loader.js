/**
 * Module de chargement dynamique des pages
 * Gère le chargement et l'affichage des différentes sections de l'interface opérateur
 */

class PageLoader {
    constructor() {
        this.currentPage = 'dashboard';
        this.pageContainer = null;
        this.pageCache = new Map();
        this.init();
    }

    init() {
        // Attendre que le DOM soit chargé
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.pageContainer = document.getElementById('pageContainer');
        if (!this.pageContainer) {
            console.error('PageLoader: Container #pageContainer non trouvé');
            return;
        }

        // Charger la page par défaut
        this.loadPage('dashboard');
    }

    /**
     * Charge une page dynamiquement
     * @param {string} pageName - Nom de la page à charger
     */
    async loadPage(pageName) {
        try {
            // Vérifier si la page est déjà en cache
            if (this.pageCache.has(pageName)) {
                this.displayPage(pageName, this.pageCache.get(pageName));
                return;
            }

            // Afficher un indicateur de chargement
            this.showLoading();

            // Construire l'URL de la page
            const pageUrl = `/operator/pages/${pageName}.html`;

            // Charger le contenu de la page
            const response = await fetch(pageUrl);
            
            if (!response.ok) {
                throw new Error(`Erreur ${response.status}: ${response.statusText}`);
            }

            const pageContent = await response.text();

            // Mettre en cache le contenu
            this.pageCache.set(pageName, pageContent);

            // Afficher la page
            this.displayPage(pageName, pageContent);

        } catch (error) {
            console.error(`Erreur lors du chargement de la page ${pageName}:`, error);
            this.showError(`Impossible de charger la page ${pageName}`);
        }
    }

    /**
     * Affiche le contenu d'une page
     * @param {string} pageName - Nom de la page
     * @param {string} content - Contenu HTML de la page
     */
    displayPage(pageName, content) {
        if (!this.pageContainer) return;

        // Injecter le contenu
        this.pageContainer.innerHTML = content;

        // Mettre à jour la page courante
        this.currentPage = pageName;

        // Mettre à jour le titre de la page
        this.updatePageTitle(pageName);

        // Mettre à jour la navigation
        this.updateNavigation(pageName);

        // Déclencher l'événement de changement de page
        this.triggerPageChangeEvent(pageName);

        // Initialiser les composants de la page si nécessaire
        this.initializePageComponents(pageName);
    }

    /**
     * Affiche un indicateur de chargement
     */
    showLoading() {
        if (!this.pageContainer) return;

        this.pageContainer.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-3 text-muted">Chargement en cours...</p>
                </div>
            </div>
        `;
    }

    /**
     * Affiche un message d'erreur
     * @param {string} message - Message d'erreur à afficher
     */
    showError(message) {
        if (!this.pageContainer) return;

        this.pageContainer.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading">Erreur de chargement</h4>
                <p>${message}</p>
                <hr>
                <p class="mb-0">
                    <button class="btn btn-outline-danger" onclick="pageLoader.loadPage('${this.currentPage}')">
                        <i class="fas fa-redo me-2"></i>Réessayer
                    </button>
                </p>
            </div>
        `;
    }

    /**
     * Met à jour le titre de la page
     * @param {string} pageName - Nom de la page
     */
    updatePageTitle(pageName) {
        const titleElement = document.getElementById('pageTitle');
        if (!titleElement) return;

        const titles = {
            dashboard: 'Tableau de bord',
            locations: 'Gestion des Lieux/Villes',
            stops: 'Gestion des Arrêts',
            amenities: 'Gestion des Commodités',
            seatPlans: 'Gestion des Plans de Sièges',
            buses: 'Gestion des Bus',
            seats: 'Gestion des Sièges',
            routes: 'Gestion des Itinéraires',
            trips: 'Gestion des Voyages',
            pricing: 'Gestion de la Tarification',
            bookings: 'Gestion des Réservations',
            payments: 'Suivi des Paiements',
            users: 'Gestion des Utilisateurs',
            validation: 'Validation des Tickets',
            reports: 'Rapports et Analyses'
        };

        titleElement.textContent = titles[pageName] || 'Page inconnue';
    }

    /**
     * Met à jour l'état de la navigation
     * @param {string} pageName - Nom de la page active
     */
    updateNavigation(pageName) {
        // Retirer la classe active de tous les liens
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Ajouter la classe active au lien correspondant
        const activeLink = document.querySelector(`.sidebar .nav-link[onclick*="'${pageName}'"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    /**
     * Déclenche un événement personnalisé lors du changement de page
     * @param {string} pageName - Nom de la nouvelle page
     */
    triggerPageChangeEvent(pageName) {
        const event = new CustomEvent('pageChanged', {
            detail: { 
                pageName: pageName,
                previousPage: this.currentPage
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * Initialise les composants spécifiques à une page
     * @param {string} pageName - Nom de la page
     */
    initializePageComponents(pageName) {
        // Cette méthode peut être étendue pour initialiser des composants spécifiques
        // selon la page chargée
        
        // Exemple : initialiser les graphiques pour le dashboard
        if (pageName === 'dashboard' && window.DashboardCharts) {
            window.DashboardCharts.init();
        }

        // Initialiser les formulaires si le module FormHandlers est disponible
        if (window.FormHandlers) {
            window.FormHandlers.initializePage(pageName);
        }

        // Charger les données si le module DataManager est disponible
        if (window.DataManager) {
            window.DataManager.loadPageData(pageName);
        }
    }

    /**
     * Vide le cache des pages
     */
    clearCache() {
        this.pageCache.clear();
    }

    /**
     * Recharge la page courante
     */
    reloadCurrentPage() {
        this.pageCache.delete(this.currentPage);
        this.loadPage(this.currentPage);
    }

    /**
     * Obtient le nom de la page courante
     * @returns {string} Nom de la page courante
     */
    getCurrentPage() {
        return this.currentPage;
    }
}

// Créer une instance globale du PageLoader
window.pageLoader = new PageLoader();

// Fonction globale pour changer de section (utilisée par les liens de navigation)
window.showSection = function(sectionName) {
    if (window.pageLoader) {
        window.pageLoader.loadPage(sectionName);
    }
};
