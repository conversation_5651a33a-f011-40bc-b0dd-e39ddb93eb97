<!-- Section Gestion des Arrêts -->
<div id="stopsSection" class="content-section">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3>Gestion des Arrêts</h3>
        <button class="btn btn-primary" onclick="showCreateStopModal()">
            <i class="fas fa-plus me-2"></i>Nouvel arrêt
        </button>
    </div>

    <!-- Filtres pour les arrêts -->
    <div class="chart-container mb-4">
        <div class="row">
            <div class="col-md-4">
                <label for="stopLocationFilter" class="form-label">Lieu</label>
                <select class="form-select" id="stopLocationFilter" onchange="filterStops()">
                    <option value="">Tous les lieux</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="stopSearchFilter" class="form-label">Rechercher</label>
                <input type="text" class="form-control" id="stopSearchFilter"
                       placeholder="Nom d'arrêt, adresse..." onkeyup="filterStops()">
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearStopFilters()">
                    <i class="fas fa-times"></i> Effacer
                </button>
            </div>
        </div>
    </div>

    <!-- Tableau des arrêts -->
    <div class="chart-container">
        <div id="stopsTableContainer">
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                <p class="mt-2 text-muted">Chargement des arrêts...</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour Arrêt -->
<div class="modal fade" id="stopModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stopModalTitle">Nouvel Arrêt</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="stopForm">
                    <input type="hidden" id="stopId">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="stopName" class="form-label">Nom de l'arrêt *</label>
                            <input type="text" class="form-control" id="stopName" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="stopLocationId" class="form-label">Lieu *</label>
                            <select class="form-select" id="stopLocationId" name="location_id" required>
                                <option value="">Sélectionner un lieu...</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="stopAddress" class="form-label">Adresse</label>
                        <textarea class="form-control" id="stopAddress" name="address" rows="2"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="stopLatitude" class="form-label">Latitude *</label>
                            <input type="number" step="any" class="form-control" id="stopLatitude" name="latitude" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="stopLongitude" class="form-label">Longitude *</label>
                            <input type="number" step="any" class="form-control" id="stopLongitude" name="longitude" required>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="saveStop()">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

<script>
// Script spécifique à la gestion des arrêts
(function() {
    'use strict';

    let stopsData = [];
    let locationsData = [];
    let filteredStops = [];

    // Initialisation de la page des arrêts
    function initStops() {
        // Écouter les événements de chargement des données
        document.addEventListener('stopsLoaded', handleStopsLoaded);
        document.addEventListener('locationsLoaded', handleLocationsLoaded);

        // Charger les données si le DataManager est disponible
        if (window.DataManager) {
            window.DataManager.loadStops();
            window.DataManager.loadLocations();
        }
    }

    // Gestion des arrêts chargés
    function handleStopsLoaded(event) {
        stopsData = event.detail || [];
        filteredStops = [...stopsData];
        updateStopsTable();
    }

    // Gestion des lieux chargés
    function handleLocationsLoaded(event) {
        locationsData = event.detail || [];
        updateLocationFilters();
    }

    // Met à jour le tableau des arrêts
    function updateStopsTable() {
        const container = document.getElementById('stopsTableContainer');
        if (!container) return;

        if (filteredStops.length === 0) {
            container.innerHTML = window.UIComponents ? 
                window.UIComponents.createEmptyState('Aucun arrêt trouvé', 'fas fa-map-pin') :
                '<div class="text-center py-4"><p class="text-muted">Aucun arrêt trouvé</p></div>';
            return;
        }

        const columns = [
            { field: 'name', title: 'Nom' },
            { field: 'location_name', title: 'Lieu' },
            { field: 'address', title: 'Adresse' },
            { 
                field: 'coordinates', 
                title: 'Coordonnées',
                formatter: (value, row) => `${row.latitude}, ${row.longitude}`
            },
            { 
                field: 'actions', 
                title: 'Actions',
                formatter: (value, row) => createActionButtons(row)
            }
        ];

        const tableHtml = window.UIComponents ? 
            window.UIComponents.createTable(columns, filteredStops) :
            createSimpleTable(filteredStops);

        container.innerHTML = tableHtml;
    }

    // Crée les boutons d'action pour chaque ligne
    function createActionButtons(stop) {
        return `
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-outline-primary" 
                        onclick="editStop(${stop.id})" title="Modifier">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger" 
                        onclick="deleteStop(${stop.id})" title="Supprimer">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
    }

    // Crée un tableau simple (fallback)
    function createSimpleTable(data) {
        let html = '<div class="table-responsive"><table class="table table-striped">';
        html += '<thead><tr><th>Nom</th><th>Lieu</th><th>Adresse</th><th>Coordonnées</th><th>Actions</th></tr></thead><tbody>';
        
        data.forEach(stop => {
            html += `
                <tr>
                    <td>${stop.name}</td>
                    <td>${stop.location_name || ''}</td>
                    <td>${stop.address || ''}</td>
                    <td>${stop.latitude}, ${stop.longitude}</td>
                    <td>${createActionButtons(stop)}</td>
                </tr>
            `;
        });
        
        html += '</tbody></table></div>';
        return html;
    }

    // Met à jour les filtres de lieux
    function updateLocationFilters() {
        const filterSelect = document.getElementById('stopLocationFilter');
        const modalSelect = document.getElementById('stopLocationId');
        
        const options = locationsData.map(location => 
            `<option value="${location.id}">${location.name} (${location.region})</option>`
        ).join('');

        if (filterSelect) {
            filterSelect.innerHTML = '<option value="">Tous les lieux</option>' + options;
        }

        if (modalSelect) {
            modalSelect.innerHTML = '<option value="">Sélectionner un lieu...</option>' + options;
        }
    }

    // Filtre les arrêts
    window.filterStops = function() {
        const locationFilter = document.getElementById('stopLocationFilter')?.value || '';
        const searchFilter = document.getElementById('stopSearchFilter')?.value.toLowerCase() || '';

        filteredStops = stopsData.filter(stop => {
            const matchesLocation = !locationFilter || stop.location_id == locationFilter;
            const matchesSearch = !searchFilter || 
                stop.name.toLowerCase().includes(searchFilter) ||
                (stop.address && stop.address.toLowerCase().includes(searchFilter)) ||
                (stop.location_name && stop.location_name.toLowerCase().includes(searchFilter));

            return matchesLocation && matchesSearch;
        });

        updateStopsTable();
    };

    // Efface les filtres
    window.clearStopFilters = function() {
        document.getElementById('stopLocationFilter').value = '';
        document.getElementById('stopSearchFilter').value = '';
        filteredStops = [...stopsData];
        updateStopsTable();
    };

    // Affiche la modal de création
    window.showCreateStopModal = function() {
        document.getElementById('stopModalTitle').textContent = 'Nouvel Arrêt';
        document.getElementById('stopId').value = '';
        
        if (window.FormHandlers) {
            window.FormHandlers.resetForm('stopForm');
        } else {
            document.getElementById('stopForm').reset();
        }

        const modal = new bootstrap.Modal(document.getElementById('stopModal'));
        modal.show();
    };

    // Édite un arrêt
    window.editStop = function(stopId) {
        const stop = stopsData.find(s => s.id === stopId);
        if (!stop) return;

        document.getElementById('stopModalTitle').textContent = 'Modifier l\'Arrêt';
        document.getElementById('stopId').value = stop.id;

        // Remplir le formulaire
        if (window.FormHandlers) {
            window.FormHandlers.populateForm('stopForm', stop);
        } else {
            document.getElementById('stopName').value = stop.name || '';
            document.getElementById('stopLocationId').value = stop.location_id || '';
            document.getElementById('stopAddress').value = stop.address || '';
            document.getElementById('stopLatitude').value = stop.latitude || '';
            document.getElementById('stopLongitude').value = stop.longitude || '';
        }

        const modal = new bootstrap.Modal(document.getElementById('stopModal'));
        modal.show();
    };

    // Sauvegarde un arrêt
    window.saveStop = function() {
        const form = document.getElementById('stopForm');
        if (!form) return;

        // Validation
        if (window.FormHandlers) {
            if (!window.FormHandlers.validateForm('stopForm')) return;
        } else if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        // Récupérer les données
        const formData = window.FormHandlers ? 
            window.FormHandlers.getFormData('stopForm') :
            new FormData(form);

        const stopData = {};
        for (let [key, value] of formData.entries()) {
            stopData[key] = value;
        }

        const stopId = document.getElementById('stopId').value;
        const isEdit = !!stopId;

        // Appel API
        if (window.ApiClient) {
            const apiCall = isEdit ? 
                window.ApiClient.updateStop(stopId, stopData) :
                window.ApiClient.createStop(stopData);

            apiCall.then(result => {
                if (window.UIComponents) {
                    window.UIComponents.showSuccess(isEdit ? 'Arrêt modifié avec succès' : 'Arrêt créé avec succès');
                }

                // Fermer la modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('stopModal'));
                modal.hide();

                // Recharger les données
                if (window.DataManager) {
                    window.DataManager.invalidateCache('stops');
                    window.DataManager.loadStops();
                }
            }).catch(error => {
                if (window.UIComponents) {
                    window.UIComponents.showError('Erreur: ' + error.message);
                }
            });
        }
    };

    // Supprime un arrêt
    window.deleteStop = function(stopId) {
        const stop = stopsData.find(s => s.id === stopId);
        if (!stop) return;

        if (!confirm(`Êtes-vous sûr de vouloir supprimer l'arrêt "${stop.name}" ?`)) {
            return;
        }

        if (window.ApiClient) {
            window.ApiClient.deleteStop(stopId)
                .then(result => {
                    if (window.UIComponents) {
                        window.UIComponents.showSuccess('Arrêt supprimé avec succès');
                    }

                    // Recharger les données
                    if (window.DataManager) {
                        window.DataManager.invalidateCache('stops');
                        window.DataManager.loadStops();
                    }
                })
                .catch(error => {
                    if (window.UIComponents) {
                        window.UIComponents.showError('Erreur: ' + error.message);
                    }
                });
        }
    };

    // Initialiser quand le DOM est prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initStops);
    } else {
        initStops();
    }

})();
</script>
