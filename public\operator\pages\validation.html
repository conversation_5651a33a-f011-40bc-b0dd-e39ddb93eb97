<!-- Section Validation des tickets -->
<div id="validationSection" class="content-section">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="chart-container text-center">
                <h3 class="mb-4"><i class="fas fa-qrcode me-2"></i>Validation des tickets</h3>
                
                <div class="mb-4">
                    <div class="input-group input-group-lg">
                        <input type="text" class="form-control" id="ticketCodeInput" 
                               placeholder="Scanner ou saisir le code du ticket" autofocus>
                        <button class="btn btn-primary" type="button" onclick="validateTicket()">
                            <i class="fas fa-check me-2"></i>Valider
                        </button>
                    </div>
                </div>

                <div id="validationResult" class="mt-4"></div>

                <div class="mt-4">
                    <button class="btn btn-outline-secondary me-2" onclick="startQRScanner()" disabled>
                        <i class="fas fa-camera me-2"></i>Scanner QR Code
                    </button>
                    <button class="btn btn-outline-info" onclick="showValidationHistory()">
                        <i class="fas fa-history me-2"></i>Historique
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Historique des validations -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3"><i class="fas fa-list me-2"></i>Validations récentes</h5>
                <div id="validationHistoryContainer">
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-2x text-muted"></i>
                        <p class="mt-2 text-muted">Aucune validation récente</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Script spécifique à la validation des tickets
(function() {
    'use strict';

    let validationHistory = [];

    // Initialisation de la page de validation
    function initValidation() {
        // Écouter l'événement Enter sur le champ de saisie
        const ticketInput = document.getElementById('ticketCodeInput');
        if (ticketInput) {
            ticketInput.addEventListener('keypress', function(event) {
                if (event.key === 'Enter') {
                    validateTicket();
                }
            });

            // Focus automatique sur le champ
            ticketInput.focus();
        }

        // Charger l'historique des validations
        loadValidationHistory();
    }

    // Valide un ticket
    window.validateTicket = function() {
        const ticketCode = document.getElementById('ticketCodeInput')?.value.trim();
        if (!ticketCode) {
            showValidationResult('error', 'Veuillez saisir un code de ticket');
            return;
        }

        // Afficher l'indicateur de chargement
        showValidationResult('loading', 'Validation en cours...');

        // Appel API pour valider le ticket
        if (window.ApiClient) {
            window.ApiClient.validateTicket(ticketCode)
                .then(result => {
                    if (result.valid) {
                        showValidationResult('success', 'Ticket valide', result);
                        addToHistory(ticketCode, 'valid', result);
                    } else {
                        showValidationResult('error', result.message || 'Ticket invalide', result);
                        addToHistory(ticketCode, 'invalid', result);
                    }
                })
                .catch(error => {
                    showValidationResult('error', 'Erreur lors de la validation: ' + error.message);
                    addToHistory(ticketCode, 'error', { error: error.message });
                })
                .finally(() => {
                    // Vider le champ et remettre le focus
                    document.getElementById('ticketCodeInput').value = '';
                    document.getElementById('ticketCodeInput').focus();
                });
        } else {
            // Simulation pour les tests
            setTimeout(() => {
                const isValid = Math.random() > 0.3; // 70% de chance d'être valide
                if (isValid) {
                    const mockResult = {
                        passenger_name: 'John Doe',
                        trip_info: 'Cotonou → Porto-Novo',
                        seat_number: 'A12',
                        departure_time: new Date().toISOString()
                    };
                    showValidationResult('success', 'Ticket valide', mockResult);
                    addToHistory(ticketCode, 'valid', mockResult);
                } else {
                    showValidationResult('error', 'Ticket invalide ou déjà utilisé');
                    addToHistory(ticketCode, 'invalid', {});
                }
                document.getElementById('ticketCodeInput').value = '';
                document.getElementById('ticketCodeInput').focus();
            }, 1000);
        }
    };

    // Affiche le résultat de validation
    function showValidationResult(type, message, data = null) {
        const container = document.getElementById('validationResult');
        if (!container) return;

        let html = '';
        let alertClass = '';
        let icon = '';

        switch (type) {
            case 'success':
                alertClass = 'alert-success';
                icon = 'fas fa-check-circle';
                break;
            case 'error':
                alertClass = 'alert-danger';
                icon = 'fas fa-times-circle';
                break;
            case 'loading':
                alertClass = 'alert-info';
                icon = 'fas fa-spinner fa-spin';
                break;
            default:
                alertClass = 'alert-info';
                icon = 'fas fa-info-circle';
        }

        html = `
            <div class="alert ${alertClass}" role="alert">
                <h5><i class="${icon} me-2"></i>${message}</h5>
        `;

        if (data && type === 'success') {
            html += `
                <hr>
                <div class="row text-start">
                    <div class="col-md-6">
                        <strong>Passager:</strong> ${data.passenger_name || 'N/A'}<br>
                        <strong>Voyage:</strong> ${data.trip_info || 'N/A'}
                    </div>
                    <div class="col-md-6">
                        <strong>Siège:</strong> ${data.seat_number || 'N/A'}<br>
                        <strong>Départ:</strong> ${data.departure_time ? window.Utils?.formatDateTime(data.departure_time) || data.departure_time : 'N/A'}
                    </div>
                </div>
            `;
        }

        html += '</div>';
        container.innerHTML = html;

        // Auto-effacement après 5 secondes pour les messages de succès/erreur
        if (type !== 'loading') {
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }
    }

    // Ajoute une validation à l'historique
    function addToHistory(ticketCode, status, data) {
        const validation = {
            id: Date.now(),
            ticket_code: ticketCode,
            status: status,
            timestamp: new Date().toISOString(),
            data: data
        };

        validationHistory.unshift(validation);
        
        // Garder seulement les 50 dernières validations
        if (validationHistory.length > 50) {
            validationHistory = validationHistory.slice(0, 50);
        }

        // Sauvegarder dans le localStorage
        if (window.Utils?.storage) {
            window.Utils.storage.set('validation_history', validationHistory);
        }

        updateValidationHistory();
    }

    // Charge l'historique des validations
    function loadValidationHistory() {
        if (window.Utils?.storage) {
            validationHistory = window.Utils.storage.get('validation_history', []);
            updateValidationHistory();
        }
    }

    // Met à jour l'affichage de l'historique
    function updateValidationHistory() {
        const container = document.getElementById('validationHistoryContainer');
        if (!container) return;

        if (validationHistory.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-history fa-2x text-muted"></i>
                    <p class="mt-2 text-muted">Aucune validation récente</p>
                </div>
            `;
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-hover">';
        html += `
            <thead>
                <tr>
                    <th>Code Ticket</th>
                    <th>Statut</th>
                    <th>Passager</th>
                    <th>Voyage</th>
                    <th>Heure</th>
                </tr>
            </thead>
            <tbody>
        `;

        validationHistory.slice(0, 20).forEach(validation => {
            const statusBadge = getStatusBadge(validation.status);
            const time = window.Utils ? window.Utils.formatDateTime(validation.timestamp) : validation.timestamp;
            const passengerName = validation.data?.passenger_name || '-';
            const tripInfo = validation.data?.trip_info || '-';

            html += `
                <tr>
                    <td><code>${validation.ticket_code}</code></td>
                    <td>${statusBadge}</td>
                    <td>${passengerName}</td>
                    <td>${tripInfo}</td>
                    <td>${time}</td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        container.innerHTML = html;
    }

    // Obtient le badge de statut
    function getStatusBadge(status) {
        const badges = {
            valid: '<span class="badge bg-success">Valide</span>',
            invalid: '<span class="badge bg-danger">Invalide</span>',
            error: '<span class="badge bg-warning">Erreur</span>'
        };
        return badges[status] || '<span class="badge bg-secondary">Inconnu</span>';
    }

    // Démarre le scanner QR (placeholder)
    window.startQRScanner = function() {
        if (window.UIComponents) {
            window.UIComponents.showInfo('Scanner QR Code non disponible dans cette version');
        } else {
            alert('Scanner QR Code non disponible dans cette version');
        }
    };

    // Affiche l'historique des validations
    window.showValidationHistory = function() {
        updateValidationHistory();
        
        // Faire défiler vers l'historique
        const historyContainer = document.getElementById('validationHistoryContainer');
        if (historyContainer) {
            historyContainer.scrollIntoView({ behavior: 'smooth' });
        }
    };

    // Efface l'historique
    window.clearValidationHistory = function() {
        if (confirm('Êtes-vous sûr de vouloir effacer l\'historique des validations ?')) {
            validationHistory = [];
            if (window.Utils?.storage) {
                window.Utils.storage.remove('validation_history');
            }
            updateValidationHistory();
            
            if (window.UIComponents) {
                window.UIComponents.showSuccess('Historique effacé');
            }
        }
    };

    // Initialiser quand le DOM est prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initValidation);
    } else {
        initValidation();
    }

})();
</script>
