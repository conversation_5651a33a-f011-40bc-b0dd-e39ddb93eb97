<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord Opérateur - EasyBus</title>
    <link rel="stylesheet" href="/./assets/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="/./assets/css/operator-dashboard.css"/>
    <link rel="stylesheet" href="/./assets/css/all.min.css"/>
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            color: white;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-bottom: 0.25rem;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s;
            border-left: 4px solid #0d6efd;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .alert-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <h4 class="text-center mb-4">
                        <i class="fas fa-bus me-2"></i>EasyBus
                        <small class="d-block fs-6 text-muted">Opérateur</small>
                    </h4>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                            <i class="fas fa-tachometer-alt me-2"></i>Tableau de bord
                        </a>

                        <!-- Gestion Géographique -->
                        <div class="nav-section-title text-muted small mt-3 mb-2 px-3">GÉOGRAPHIE</div>
                        <a class="nav-link" href="#" onclick="showSection('locations')">
                            <i class="fas fa-map-marker-alt me-2"></i>Lieux/Villes
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('stops')">
                            <i class="fas fa-map-pin me-2"></i>Arrêts
                        </a>

                        <!-- Gestion Flotte -->
                        <div class="nav-section-title text-muted small mt-3 mb-2 px-3">FLOTTE</div>
                        <a class="nav-link" href="#" onclick="showSection('amenities')">
                            <i class="fas fa-star me-2"></i>Commodités
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('seatPlans')">
                            <i class="fas fa-th me-2"></i>Plans de sièges
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('buses')">
                            <i class="fas fa-bus me-2"></i>Bus
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('seats')">
                            <i class="fas fa-chair me-2"></i>Sièges
                        </a>

                        <!-- Gestion Voyages -->
                        <div class="nav-section-title text-muted small mt-3 mb-2 px-3">VOYAGES</div>
                        <a class="nav-link" href="#" onclick="showSection('routes')">
                            <i class="fas fa-road me-2"></i>Itinéraires
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('trips')">
                            <i class="fas fa-route me-2"></i>Voyages
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('pricing')">
                            <i class="fas fa-tags me-2"></i>Tarification
                        </a>

                        <!-- Gestion Opérationnelle -->
                        <div class="nav-section-title text-muted small mt-3 mb-2 px-3">OPÉRATIONS</div>
                        <a class="nav-link" href="#" onclick="showSection('bookings')">
                            <i class="fas fa-ticket-alt me-2"></i>Réservations
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('payments')">
                            <i class="fas fa-credit-card me-2"></i>Paiements
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('users')">
                            <i class="fas fa-users me-2"></i>Utilisateurs
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('validation')">
                            <i class="fas fa-qrcode me-2"></i>Validation tickets
                        </a>

                        <!-- Rapports -->
                        <div class="nav-section-title text-muted small mt-3 mb-2 px-3">ANALYSES</div>
                        <a class="nav-link" href="#" onclick="showSection('reports')">
                            <i class="fas fa-chart-bar me-2"></i>Rapports
                        </a>

                        <hr class="my-3">
                        <a class="nav-link" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Contenu principal -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Header -->
                <div class="bg-white shadow-sm p-3 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="mb-0" id="pageTitle">Tableau de bord</h2>
                        <div class="d-flex align-items-center">
                            <span class="me-3">Bonjour, <strong id="operatorName">Opérateur</strong></span>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user-circle"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profil</a></li>
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Paramètres</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Déconnexion</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Conteneur pour les pages dynamiques -->
                <div id="pageContainer">
                    <!-- Le contenu des pages sera chargé ici dynamiquement -->
                </div>

                <!-- Conteneur pour les alertes -->
                <div id="alertContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;">
                    <!-- Les alertes seront affichées ici -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/./assets/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.9/dist/chart.umd.min.js"></script>
    
    <!-- Modules JavaScript -->
    <script src="/./assets/js/modules/page-loader.js" defer></script>
    <script src="/./assets/js/modules/data-manager.js" defer></script>
    <script src="/./assets/js/modules/api-client.js" defer></script>
    <script src="/./assets/js/modules/ui-components.js" defer></script>
    <script src="/./assets/js/modules/form-handlers.js" defer></script>
    <script src="/./assets/js/modules/utils.js" defer></script>
    
    <!-- Scripts principaux -->
    <script src="/./assets/js/operator-dashboard.js" defer></script>
    <script src="/./assets/js/modules/operator-crud.js" defer></script>
    <script src="/./assets/js/auth.js" defer></script>
</body>
</html>
