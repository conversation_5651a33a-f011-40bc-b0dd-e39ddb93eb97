<!-- Section Gestion des Itinéraires -->
<div id="routesSection" class="content-section">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3>Gestion des Itinéraires</h3>
        <button class="btn btn-primary" onclick="showCreateRouteModal()">
            <i class="fas fa-plus me-2"></i>Nouvel itinéraire
        </button>
    </div>

    <!-- Filtres pour les itinéraires -->
    <div class="chart-container mb-4">
        <div class="row">
            <div class="col-md-3">
                <label for="routeStatusFilter" class="form-label">Statut</label>
                <select class="form-select" id="routeStatusFilter" onchange="filterRoutes()">
                    <option value="">Tous les statuts</option>
                    <option value="active">Actif</option>
                    <option value="inactive">Inactif</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="routeDepartureFilter" class="form-label">Départ</label>
                <select class="form-select" id="routeDepartureFilter" onchange="filterRoutes()">
                    <option value="">Tous les départs</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="routeSearchFilter" class="form-label">Rechercher</label>
                <input type="text" class="form-control" id="routeSearchFilter"
                       placeholder="Nom d'itinéraire..." onkeyup="filterRoutes()">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearRouteFilters()">
                    <i class="fas fa-times"></i> Effacer
                </button>
            </div>
        </div>
    </div>

    <!-- Tableau des itinéraires -->
    <div class="chart-container">
        <div id="routesTableContainer">
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                <p class="mt-2 text-muted">Chargement des itinéraires...</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour Itinéraire -->
<div class="modal fade" id="routeModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="routeModalTitle">Nouvel Itinéraire</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="routeForm">
                    <input type="hidden" id="routeId">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="routeName" class="form-label">Nom de l'itinéraire *</label>
                            <input type="text" class="form-control" id="routeName" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="routeStatus" class="form-label">Statut</label>
                            <select class="form-select" id="routeStatus" name="status">
                                <option value="active">Actif</option>
                                <option value="inactive">Inactif</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="routeDepartureStopId" class="form-label">Arrêt de départ *</label>
                            <select class="form-select" id="routeDepartureStopId" name="departure_stop_id" required>
                                <option value="">Sélectionner un arrêt...</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="routeArrivalStopId" class="form-label">Arrêt d'arrivée *</label>
                            <select class="form-select" id="routeArrivalStopId" name="arrival_stop_id" required>
                                <option value="">Sélectionner un arrêt...</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="routeDistance" class="form-label">Distance (km)</label>
                            <input type="number" step="0.1" class="form-control" id="routeDistance" name="distance">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="routeDuration" class="form-label">Durée estimée (minutes)</label>
                            <input type="number" class="form-control" id="routeDuration" name="estimated_duration">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="routeBasePrice" class="form-label">Prix de base (FCFA)</label>
                            <input type="number" class="form-control" id="routeBasePrice" name="base_price">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="routeDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="routeDescription" name="description" rows="3"></textarea>
                    </div>
                    
                    <!-- Section des arrêts intermédiaires -->
                    <div class="mb-3">
                        <h6>Arrêts intermédiaires</h6>
                        <div id="intermediateStopsContainer">
                            <p class="text-muted">Aucun arrêt intermédiaire ajouté</p>
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addIntermediateStop()">
                            <i class="fas fa-plus me-2"></i>Ajouter un arrêt
                        </button>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="saveRoute()">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

<script>
// Script spécifique à la gestion des itinéraires
(function() {
    'use strict';

    let routesData = [];
    let stopsData = [];
    let filteredRoutes = [];
    let intermediateStopsCount = 0;

    // Initialisation de la page des itinéraires
    function initRoutes() {
        // Écouter les événements de chargement des données
        document.addEventListener('routesLoaded', handleRoutesLoaded);
        document.addEventListener('stopsLoaded', handleStopsLoaded);

        // Charger les données si le DataManager est disponible
        if (window.DataManager) {
            window.DataManager.loadRoutes();
            window.DataManager.loadStops();
        }
    }

    // Gestion des itinéraires chargés
    function handleRoutesLoaded(event) {
        routesData = event.detail || [];
        filteredRoutes = [...routesData];
        updateRoutesTable();
    }

    // Gestion des arrêts chargés
    function handleStopsLoaded(event) {
        stopsData = event.detail || [];
        updateStopSelects();
    }

    // Met à jour le tableau des itinéraires
    function updateRoutesTable() {
        const container = document.getElementById('routesTableContainer');
        if (!container) return;

        if (filteredRoutes.length === 0) {
            container.innerHTML = window.UIComponents ? 
                window.UIComponents.createEmptyState('Aucun itinéraire trouvé', 'fas fa-road') :
                '<div class="text-center py-4"><p class="text-muted">Aucun itinéraire trouvé</p></div>';
            return;
        }

        const columns = [
            { field: 'name', title: 'Nom' },
            { 
                field: 'route', 
                title: 'Trajet',
                formatter: (value, row) => `${row.departure_stop_name} → ${row.arrival_stop_name}`
            },
            { 
                field: 'distance', 
                title: 'Distance',
                formatter: (value) => value ? `${value} km` : 'N/A'
            },
            { 
                field: 'estimated_duration', 
                title: 'Durée',
                formatter: (value) => value ? `${value} min` : 'N/A'
            },
            { 
                field: 'base_price', 
                title: 'Prix de base',
                formatter: (value) => window.Utils ? window.Utils.formatCurrency(value) : `${value} FCFA`
            },
            { 
                field: 'status', 
                title: 'Statut',
                formatter: (value) => window.UIComponents ? 
                    window.UIComponents.createStatusBadge(value) : value
            },
            { 
                field: 'actions', 
                title: 'Actions',
                formatter: (value, row) => createActionButtons(row)
            }
        ];

        const tableHtml = window.UIComponents ? 
            window.UIComponents.createTable(columns, filteredRoutes) :
            createSimpleTable(filteredRoutes);

        container.innerHTML = tableHtml;
    }

    // Crée les boutons d'action pour chaque ligne
    function createActionButtons(route) {
        return `
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-outline-primary" 
                        onclick="editRoute(${route.id})" title="Modifier">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-info" 
                        onclick="viewRouteStops(${route.id})" title="Voir les arrêts">
                    <i class="fas fa-map-pin"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger" 
                        onclick="deleteRoute(${route.id})" title="Supprimer">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
    }

    // Crée un tableau simple (fallback)
    function createSimpleTable(data) {
        let html = '<div class="table-responsive"><table class="table table-striped">';
        html += '<thead><tr><th>Nom</th><th>Trajet</th><th>Distance</th><th>Durée</th><th>Prix</th><th>Statut</th><th>Actions</th></tr></thead><tbody>';
        
        data.forEach(route => {
            html += `
                <tr>
                    <td>${route.name}</td>
                    <td>${route.departure_stop_name} → ${route.arrival_stop_name}</td>
                    <td>${route.distance ? route.distance + ' km' : 'N/A'}</td>
                    <td>${route.estimated_duration ? route.estimated_duration + ' min' : 'N/A'}</td>
                    <td>${route.base_price ? route.base_price + ' FCFA' : 'N/A'}</td>
                    <td><span class="badge bg-${route.status === 'active' ? 'success' : 'secondary'}">${route.status}</span></td>
                    <td>${createActionButtons(route)}</td>
                </tr>
            `;
        });
        
        html += '</tbody></table></div>';
        return html;
    }

    // Met à jour les selects d'arrêts
    function updateStopSelects() {
        const departureSelect = document.getElementById('routeDepartureStopId');
        const arrivalSelect = document.getElementById('routeArrivalStopId');
        const departureFilter = document.getElementById('routeDepartureFilter');
        
        const options = stopsData.map(stop => 
            `<option value="${stop.id}">${stop.name} (${stop.location_name})</option>`
        ).join('');

        if (departureSelect) {
            departureSelect.innerHTML = '<option value="">Sélectionner un arrêt...</option>' + options;
        }

        if (arrivalSelect) {
            arrivalSelect.innerHTML = '<option value="">Sélectionner un arrêt...</option>' + options;
        }

        if (departureFilter) {
            departureFilter.innerHTML = '<option value="">Tous les départs</option>' + options;
        }
    }

    // Filtre les itinéraires
    window.filterRoutes = function() {
        const statusFilter = document.getElementById('routeStatusFilter')?.value || '';
        const departureFilter = document.getElementById('routeDepartureFilter')?.value || '';
        const searchFilter = document.getElementById('routeSearchFilter')?.value.toLowerCase() || '';

        filteredRoutes = routesData.filter(route => {
            const matchesStatus = !statusFilter || route.status === statusFilter;
            const matchesDeparture = !departureFilter || route.departure_stop_id == departureFilter;
            const matchesSearch = !searchFilter || 
                route.name.toLowerCase().includes(searchFilter) ||
                (route.departure_stop_name && route.departure_stop_name.toLowerCase().includes(searchFilter)) ||
                (route.arrival_stop_name && route.arrival_stop_name.toLowerCase().includes(searchFilter));

            return matchesStatus && matchesDeparture && matchesSearch;
        });

        updateRoutesTable();
    };

    // Efface les filtres
    window.clearRouteFilters = function() {
        document.getElementById('routeStatusFilter').value = '';
        document.getElementById('routeDepartureFilter').value = '';
        document.getElementById('routeSearchFilter').value = '';
        filteredRoutes = [...routesData];
        updateRoutesTable();
    };

    // Affiche la modal de création
    window.showCreateRouteModal = function() {
        document.getElementById('routeModalTitle').textContent = 'Nouvel Itinéraire';
        document.getElementById('routeId').value = '';
        intermediateStopsCount = 0;
        
        if (window.FormHandlers) {
            window.FormHandlers.resetForm('routeForm');
        } else {
            document.getElementById('routeForm').reset();
        }

        // Réinitialiser les arrêts intermédiaires
        document.getElementById('intermediateStopsContainer').innerHTML = '<p class="text-muted">Aucun arrêt intermédiaire ajouté</p>';

        const modal = new bootstrap.Modal(document.getElementById('routeModal'));
        modal.show();
    };

    // Ajoute un arrêt intermédiaire
    window.addIntermediateStop = function() {
        const container = document.getElementById('intermediateStopsContainer');
        
        // Supprimer le message "Aucun arrêt" s'il existe
        if (container.querySelector('.text-muted')) {
            container.innerHTML = '';
        }

        const stopId = `intermediateStop_${intermediateStopsCount++}`;
        const stopHtml = `
            <div class="row mb-2" id="${stopId}">
                <div class="col-md-6">
                    <select class="form-select" name="intermediate_stops[]">
                        <option value="">Sélectionner un arrêt...</option>
                        ${stopsData.map(stop => `<option value="${stop.id}">${stop.name} (${stop.location_name})</option>`).join('')}
                    </select>
                </div>
                <div class="col-md-4">
                    <input type="number" class="form-control" name="stop_order[]" placeholder="Ordre" min="1">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeIntermediateStop('${stopId}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', stopHtml);
    };

    // Supprime un arrêt intermédiaire
    window.removeIntermediateStop = function(stopId) {
        const stopElement = document.getElementById(stopId);
        if (stopElement) {
            stopElement.remove();
        }

        // Si plus d'arrêts, afficher le message
        const container = document.getElementById('intermediateStopsContainer');
        if (container.children.length === 0) {
            container.innerHTML = '<p class="text-muted">Aucun arrêt intermédiaire ajouté</p>';
        }
    };

    // Autres fonctions globales...
    window.editRoute = function(routeId) {
        // Implémentation de l'édition
        if (window.UIComponents) {
            window.UIComponents.showInfo('Édition d\'itinéraire - Fonctionnalité en cours de développement');
        }
    };

    window.viewRouteStops = function(routeId) {
        // Implémentation de la vue des arrêts
        if (window.UIComponents) {
            window.UIComponents.showInfo('Vue des arrêts - Fonctionnalité en cours de développement');
        }
    };

    window.deleteRoute = function(routeId) {
        // Implémentation de la suppression
        if (window.UIComponents) {
            window.UIComponents.showInfo('Suppression d\'itinéraire - Fonctionnalité en cours de développement');
        }
    };

    window.saveRoute = function() {
        // Implémentation de la sauvegarde
        if (window.UIComponents) {
            window.UIComponents.showInfo('Sauvegarde d\'itinéraire - Fonctionnalité en cours de développement');
        }
    };

    // Initialiser quand le DOM est prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initRoutes);
    } else {
        initRoutes();
    }

})();
</script>
