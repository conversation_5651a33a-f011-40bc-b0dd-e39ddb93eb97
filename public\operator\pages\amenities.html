<!-- Section Gestion des Commodités -->
<div id="amenitiesSection" class="content-section">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3>Gestion des Commodités</h3>
        <button class="btn btn-primary" onclick="showCreateAmenityModal()">
            <i class="fas fa-plus me-2"></i>Nouvelle commodité
        </button>
    </div>

    <!-- Grille des commodités -->
    <div id="amenitiesGridContainer">
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
            <p class="mt-2 text-muted">Chargement des commodités...</p>
        </div>
    </div>
</div>

<!-- Modal pour Commodité -->
<div class="modal fade" id="amenityModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="amenityModalTitle">Nouvelle Commodité</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="amenityForm">
                    <input type="hidden" id="amenityId">
                    <div class="mb-3">
                        <label for="amenityName" class="form-label">Nom de la commodité *</label>
                        <input type="text" class="form-control" id="amenityName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="amenityDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="amenityDescription" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="amenityIcon" class="form-label">Icône (classe FontAwesome)</label>
                        <input type="text" class="form-control" id="amenityIcon" name="icon" placeholder="fas fa-wifi">
                        <div class="form-text">Exemple: fas fa-wifi, fas fa-coffee, fas fa-tv</div>
                    </div>
                    <div class="mb-3">
                        <label for="amenityStatus" class="form-label">Statut</label>
                        <select class="form-select" id="amenityStatus" name="status">
                            <option value="active">Actif</option>
                            <option value="inactive">Inactif</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="saveAmenity()">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

<script>
// Script spécifique à la gestion des commodités
(function() {
    'use strict';

    let amenitiesData = [];

    // Initialisation de la page des commodités
    function initAmenities() {
        // Écouter l'événement de chargement des commodités
        document.addEventListener('amenitiesLoaded', handleAmenitiesLoaded);

        // Charger les données si le DataManager est disponible
        if (window.DataManager) {
            window.DataManager.loadAmenities();
        }
    }

    // Gestion des commodités chargées
    function handleAmenitiesLoaded(event) {
        amenitiesData = event.detail || [];
        updateAmenitiesGrid();
    }

    // Met à jour la grille des commodités
    function updateAmenitiesGrid() {
        const container = document.getElementById('amenitiesGridContainer');
        if (!container) return;

        if (amenitiesData.length === 0) {
            container.innerHTML = window.UIComponents ? 
                window.UIComponents.createEmptyState('Aucune commodité trouvée', 'fas fa-star') :
                '<div class="text-center py-4"><p class="text-muted">Aucune commodité trouvée</p></div>';
            return;
        }

        // Créer la grille de cartes
        let html = '<div class="row">';
        amenitiesData.forEach(amenity => {
            html += `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="${amenity.icon || 'fas fa-star'} fa-3x text-primary"></i>
                            </div>
                            <h5 class="card-title">${amenity.name}</h5>
                            <p class="card-text text-muted">${amenity.description || ''}</p>
                            <div class="mb-3">
                                ${window.UIComponents ? 
                                    window.UIComponents.createStatusBadge(amenity.status) : 
                                    `<span class="badge bg-${amenity.status === 'active' ? 'success' : 'secondary'}">${amenity.status}</span>`
                                }
                            </div>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="editAmenity(${amenity.id})">
                                    <i class="fas fa-edit"></i> Modifier
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="deleteAmenity(${amenity.id})">
                                    <i class="fas fa-trash"></i> Supprimer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';

        container.innerHTML = html;
    }

    // Affiche la modal de création
    window.showCreateAmenityModal = function() {
        document.getElementById('amenityModalTitle').textContent = 'Nouvelle Commodité';
        document.getElementById('amenityId').value = '';
        
        if (window.FormHandlers) {
            window.FormHandlers.resetForm('amenityForm');
        } else {
            document.getElementById('amenityForm').reset();
        }

        const modal = new bootstrap.Modal(document.getElementById('amenityModal'));
        modal.show();
    };

    // Édite une commodité
    window.editAmenity = function(amenityId) {
        const amenity = amenitiesData.find(a => a.id === amenityId);
        if (!amenity) return;

        document.getElementById('amenityModalTitle').textContent = 'Modifier la Commodité';
        document.getElementById('amenityId').value = amenity.id;

        // Remplir le formulaire
        if (window.FormHandlers) {
            window.FormHandlers.populateForm('amenityForm', amenity);
        } else {
            document.getElementById('amenityName').value = amenity.name || '';
            document.getElementById('amenityDescription').value = amenity.description || '';
            document.getElementById('amenityIcon').value = amenity.icon || '';
            document.getElementById('amenityStatus').value = amenity.status || 'active';
        }

        const modal = new bootstrap.Modal(document.getElementById('amenityModal'));
        modal.show();
    };

    // Sauvegarde une commodité
    window.saveAmenity = function() {
        const form = document.getElementById('amenityForm');
        if (!form) return;

        // Validation
        if (window.FormHandlers) {
            if (!window.FormHandlers.validateForm('amenityForm')) return;
        } else if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        // Récupérer les données
        const formData = window.FormHandlers ? 
            window.FormHandlers.getFormData('amenityForm') :
            new FormData(form);

        const amenityData = {};
        for (let [key, value] of formData.entries()) {
            amenityData[key] = value;
        }

        const amenityId = document.getElementById('amenityId').value;
        const isEdit = !!amenityId;

        // Appel API
        if (window.ApiClient) {
            const apiCall = isEdit ? 
                window.ApiClient.put(`/operator/amenities/${amenityId}`, amenityData) :
                window.ApiClient.post('/operator/amenities', amenityData);

            apiCall.then(result => {
                if (window.UIComponents) {
                    window.UIComponents.showSuccess(isEdit ? 'Commodité modifiée avec succès' : 'Commodité créée avec succès');
                }

                // Fermer la modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('amenityModal'));
                modal.hide();

                // Recharger les données
                if (window.DataManager) {
                    window.DataManager.invalidateCache('amenities');
                    window.DataManager.loadAmenities();
                }
            }).catch(error => {
                if (window.UIComponents) {
                    window.UIComponents.showError('Erreur: ' + error.message);
                }
            });
        }
    };

    // Supprime une commodité
    window.deleteAmenity = function(amenityId) {
        const amenity = amenitiesData.find(a => a.id === amenityId);
        if (!amenity) return;

        if (!confirm(`Êtes-vous sûr de vouloir supprimer la commodité "${amenity.name}" ?`)) {
            return;
        }

        if (window.ApiClient) {
            window.ApiClient.delete(`/operator/amenities/${amenityId}`)
                .then(result => {
                    if (window.UIComponents) {
                        window.UIComponents.showSuccess('Commodité supprimée avec succès');
                    }

                    // Recharger les données
                    if (window.DataManager) {
                        window.DataManager.invalidateCache('amenities');
                        window.DataManager.loadAmenities();
                    }
                })
                .catch(error => {
                    if (window.UIComponents) {
                        window.UIComponents.showError('Erreur: ' + error.message);
                    }
                });
        }
    };

    // Initialiser quand le DOM est prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAmenities);
    } else {
        initAmenities();
    }

})();
</script>
