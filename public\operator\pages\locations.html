<!-- Section Gestion des Lieux -->
<div id="locationsSection" class="content-section">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3>Gestion des Lieux/Villes</h3>
        <button class="btn btn-primary" onclick="showCreateLocationModal()">
            <i class="fas fa-plus me-2"></i>Nouveau lieu
        </button>
    </div>

    <!-- Filtres pour les lieux -->
    <div class="chart-container mb-4">
        <div class="row">
            <div class="col-md-3">
                <label for="locationStatusFilter" class="form-label">Statut</label>
                <select class="form-select" id="locationStatusFilter" onchange="filterLocations()">
                    <option value="">Tous les statuts</option>
                    <option value="active">Actif</option>
                    <option value="inactive">Inactif</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="locationCountryFilter" class="form-label">Pays</label>
                <select class="form-select" id="locationCountryFilter" onchange="filterLocations()">
                    <option value="">Tous les pays</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="locationSearchFilter" class="form-label">Rechercher</label>
                <input type="text" class="form-control" id="locationSearchFilter"
                       placeholder="Nom de lieu, région..." onkeyup="filterLocations()">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearLocationFilters()">
                    <i class="fas fa-times"></i> Effacer
                </button>
            </div>
        </div>
    </div>

    <!-- Tableau des lieux -->
    <div class="chart-container">
        <div id="locationsTableContainer">
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                <p class="mt-2 text-muted">Chargement des lieux...</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour Lieu/Ville -->
<div class="modal fade" id="locationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="locationModalTitle">Nouveau Lieu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="locationForm">
                    <input type="hidden" id="locationId">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="locationName" class="form-label">Nom du lieu *</label>
                            <input type="text" class="form-control" id="locationName" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="locationRegion" class="form-label">Région *</label>
                            <input type="text" class="form-control" id="locationRegion" name="region" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="locationCountry" class="form-label">Pays *</label>
                            <input type="text" class="form-control" id="locationCountry" name="country" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="locationTimeZone" class="form-label">Fuseau horaire *</label>
                            <select class="form-select" id="locationTimeZone" name="timezone" required>
                                <option value="">Sélectionner...</option>
                                <option value="Africa/Porto-Novo">Africa/Porto-Novo</option>
                                <option value="Africa/Abidjan">Africa/Abidjan</option>
                                <option value="Africa/Accra">Africa/Accra</option>
                                <option value="Africa/Bamako">Africa/Bamako</option>
                                <option value="Africa/Ouagadougou">Africa/Ouagadougou</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="locationLatitude" class="form-label">Latitude *</label>
                            <input type="number" step="any" class="form-control" id="locationLatitude" name="latitude" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="locationLongitude" class="form-label">Longitude *</label>
                            <input type="number" step="any" class="form-control" id="locationLongitude" name="longitude" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="locationStatus" class="form-label">Statut</label>
                        <select class="form-select" id="locationStatus" name="status">
                            <option value="active">Actif</option>
                            <option value="inactive">Inactif</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="saveLocation()">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

<script>
// Script spécifique à la gestion des lieux
(function() {
    'use strict';

    let locationsData = [];
    let filteredLocations = [];

    // Initialisation de la page des lieux
    function initLocations() {
        // Écouter l'événement de chargement des lieux
        document.addEventListener('locationsLoaded', handleLocationsLoaded);

        // Charger les données si le DataManager est disponible
        if (window.DataManager) {
            window.DataManager.loadLocations();
        }
    }

    // Gestion des lieux chargés
    function handleLocationsLoaded(event) {
        locationsData = event.detail || [];
        filteredLocations = [...locationsData];
        
        // Mettre à jour l'interface
        updateLocationsTable();
        updateCountryFilter();
    }

    // Met à jour le tableau des lieux
    function updateLocationsTable() {
        const container = document.getElementById('locationsTableContainer');
        if (!container) return;

        if (filteredLocations.length === 0) {
            container.innerHTML = window.UIComponents ? 
                window.UIComponents.createEmptyState('Aucun lieu trouvé', 'fas fa-map-marker-alt') :
                '<div class="text-center py-4"><p class="text-muted">Aucun lieu trouvé</p></div>';
            return;
        }

        const columns = [
            { field: 'name', title: 'Nom' },
            { field: 'region', title: 'Région' },
            { field: 'country', title: 'Pays' },
            { field: 'timezone', title: 'Fuseau horaire' },
            { 
                field: 'status', 
                title: 'Statut',
                formatter: (value) => window.UIComponents ? 
                    window.UIComponents.createStatusBadge(value) : value
            },
            { 
                field: 'actions', 
                title: 'Actions',
                formatter: (value, row) => createActionButtons(row)
            }
        ];

        const tableHtml = window.UIComponents ? 
            window.UIComponents.createTable(columns, filteredLocations) :
            createSimpleTable(filteredLocations);

        container.innerHTML = tableHtml;
    }

    // Crée les boutons d'action pour chaque ligne
    function createActionButtons(location) {
        return `
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-outline-primary" 
                        onclick="editLocation(${location.id})" title="Modifier">
                    <i class="fas fa-edit"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger" 
                        onclick="deleteLocation(${location.id})" title="Supprimer">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
    }

    // Crée un tableau simple (fallback)
    function createSimpleTable(data) {
        let html = '<div class="table-responsive"><table class="table table-striped">';
        html += '<thead><tr><th>Nom</th><th>Région</th><th>Pays</th><th>Statut</th><th>Actions</th></tr></thead><tbody>';
        
        data.forEach(location => {
            html += `
                <tr>
                    <td>${location.name}</td>
                    <td>${location.region}</td>
                    <td>${location.country}</td>
                    <td><span class="badge bg-${location.status === 'active' ? 'success' : 'secondary'}">${location.status}</span></td>
                    <td>${createActionButtons(location)}</td>
                </tr>
            `;
        });
        
        html += '</tbody></table></div>';
        return html;
    }

    // Met à jour le filtre des pays
    function updateCountryFilter() {
        const select = document.getElementById('locationCountryFilter');
        if (!select) return;

        // Obtenir la liste unique des pays
        const countries = [...new Set(locationsData.map(location => location.country))].sort();
        
        // Vider et repeupler le select
        select.innerHTML = '<option value="">Tous les pays</option>';
        countries.forEach(country => {
            select.innerHTML += `<option value="${country}">${country}</option>`;
        });
    }

    // Filtre les lieux
    window.filterLocations = function() {
        const statusFilter = document.getElementById('locationStatusFilter')?.value || '';
        const countryFilter = document.getElementById('locationCountryFilter')?.value || '';
        const searchFilter = document.getElementById('locationSearchFilter')?.value.toLowerCase() || '';

        filteredLocations = locationsData.filter(location => {
            const matchesStatus = !statusFilter || location.status === statusFilter;
            const matchesCountry = !countryFilter || location.country === countryFilter;
            const matchesSearch = !searchFilter || 
                location.name.toLowerCase().includes(searchFilter) ||
                location.region.toLowerCase().includes(searchFilter) ||
                location.country.toLowerCase().includes(searchFilter);

            return matchesStatus && matchesCountry && matchesSearch;
        });

        updateLocationsTable();
    };

    // Efface les filtres
    window.clearLocationFilters = function() {
        document.getElementById('locationStatusFilter').value = '';
        document.getElementById('locationCountryFilter').value = '';
        document.getElementById('locationSearchFilter').value = '';
        filteredLocations = [...locationsData];
        updateLocationsTable();
    };

    // Affiche la modal de création
    window.showCreateLocationModal = function() {
        document.getElementById('locationModalTitle').textContent = 'Nouveau Lieu';
        document.getElementById('locationId').value = '';
        
        if (window.FormHandlers) {
            window.FormHandlers.resetForm('locationForm');
        } else {
            document.getElementById('locationForm').reset();
        }

        const modal = new bootstrap.Modal(document.getElementById('locationModal'));
        modal.show();
    };

    // Édite un lieu
    window.editLocation = function(locationId) {
        const location = locationsData.find(l => l.id === locationId);
        if (!location) return;

        document.getElementById('locationModalTitle').textContent = 'Modifier le Lieu';
        document.getElementById('locationId').value = location.id;

        // Remplir le formulaire
        if (window.FormHandlers) {
            window.FormHandlers.populateForm('locationForm', location);
        } else {
            document.getElementById('locationName').value = location.name || '';
            document.getElementById('locationRegion').value = location.region || '';
            document.getElementById('locationCountry').value = location.country || '';
            document.getElementById('locationTimeZone').value = location.timezone || '';
            document.getElementById('locationLatitude').value = location.latitude || '';
            document.getElementById('locationLongitude').value = location.longitude || '';
            document.getElementById('locationStatus').value = location.status || 'active';
        }

        const modal = new bootstrap.Modal(document.getElementById('locationModal'));
        modal.show();
    };

    // Sauvegarde un lieu
    window.saveLocation = function() {
        const form = document.getElementById('locationForm');
        if (!form) return;

        // Validation
        if (window.FormHandlers) {
            if (!window.FormHandlers.validateForm('locationForm')) return;
        } else if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        // Récupérer les données
        const formData = window.FormHandlers ? 
            window.FormHandlers.getFormData('locationForm') :
            new FormData(form);

        const locationData = {};
        for (let [key, value] of formData.entries()) {
            locationData[key] = value;
        }

        const locationId = document.getElementById('locationId').value;
        const isEdit = !!locationId;

        // Appel API
        if (window.ApiClient) {
            const apiCall = isEdit ? 
                window.ApiClient.updateLocation(locationId, locationData) :
                window.ApiClient.createLocation(locationData);

            apiCall.then(result => {
                if (window.UIComponents) {
                    window.UIComponents.showSuccess(isEdit ? 'Lieu modifié avec succès' : 'Lieu créé avec succès');
                }

                // Fermer la modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('locationModal'));
                modal.hide();

                // Recharger les données
                if (window.DataManager) {
                    window.DataManager.invalidateCache('locations');
                    window.DataManager.loadLocations();
                }
            }).catch(error => {
                if (window.UIComponents) {
                    window.UIComponents.showError('Erreur: ' + error.message);
                }
            });
        }
    };

    // Supprime un lieu
    window.deleteLocation = function(locationId) {
        const location = locationsData.find(l => l.id === locationId);
        if (!location) return;

        if (!confirm(`Êtes-vous sûr de vouloir supprimer le lieu "${location.name}" ?`)) {
            return;
        }

        if (window.ApiClient) {
            window.ApiClient.deleteLocation(locationId)
                .then(result => {
                    if (window.UIComponents) {
                        window.UIComponents.showSuccess('Lieu supprimé avec succès');
                    }

                    // Recharger les données
                    if (window.DataManager) {
                        window.DataManager.invalidateCache('locations');
                        window.DataManager.loadLocations();
                    }
                })
                .catch(error => {
                    if (window.UIComponents) {
                        window.UIComponents.showError('Erreur: ' + error.message);
                    }
                });
        }
    };

    // Initialiser quand le DOM est prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initLocations);
    } else {
        initLocations();
    }

})();
</script>
