/**
 * Module client API centralisé
 * Gère toutes les communications avec l'API backend
 */

class ApiClient {
    constructor() {
        this.baseUrl = '/api';
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
        this.token = null;
        this.init();
    }

    init() {
        // Récupérer le token d'authentification depuis le localStorage
        this.token = localStorage.getItem('operator_token');
        
        // Ajouter le token aux headers par défaut si disponible
        if (this.token) {
            this.defaultHeaders['Authorization'] = `Bearer ${this.token}`;
        }
    }

    /**
     * Effectue une requête HTTP
     * @param {string} method - Méthode HTTP (GET, POST, PUT, DELETE)
     * @param {string} endpoint - Point de terminaison de l'API
     * @param {Object} data - Données à envoyer (pour POST/PUT)
     * @param {Object} options - Options supplémentaires
     * @returns {Promise} Réponse de l'API
     */
    async request(method, endpoint, data = null, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        
        const config = {
            method: method.toUpperCase(),
            headers: {
                ...this.defaultHeaders,
                ...options.headers
            },
            ...options
        };

        // Ajouter les données pour POST/PUT
        if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
            if (data instanceof FormData) {
                // Pour FormData, ne pas définir Content-Type (laissé au navigateur)
                delete config.headers['Content-Type'];
                config.body = data;
            } else {
                config.body = JSON.stringify(data);
            }
        }

        try {
            const response = await fetch(url, config);
            
            // Gérer les erreurs d'authentification
            if (response.status === 401) {
                this.handleAuthError();
                throw new Error('Non autorisé');
            }

            // Gérer les autres erreurs HTTP
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `Erreur HTTP ${response.status}`);
            }

            // Retourner les données JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return await response.json();
            }
            
            return await response.text();

        } catch (error) {
            console.error(`Erreur API ${method} ${endpoint}:`, error);
            throw error;
        }
    }

    /**
     * Requête GET
     * @param {string} endpoint - Point de terminaison
     * @param {Object} options - Options supplémentaires
     * @returns {Promise} Réponse de l'API
     */
    async get(endpoint, options = {}) {
        return this.request('GET', endpoint, null, options);
    }

    /**
     * Requête POST
     * @param {string} endpoint - Point de terminaison
     * @param {Object} data - Données à envoyer
     * @param {Object} options - Options supplémentaires
     * @returns {Promise} Réponse de l'API
     */
    async post(endpoint, data, options = {}) {
        return this.request('POST', endpoint, data, options);
    }

    /**
     * Requête PUT
     * @param {string} endpoint - Point de terminaison
     * @param {Object} data - Données à envoyer
     * @param {Object} options - Options supplémentaires
     * @returns {Promise} Réponse de l'API
     */
    async put(endpoint, data, options = {}) {
        return this.request('PUT', endpoint, data, options);
    }

    /**
     * Requête PATCH
     * @param {string} endpoint - Point de terminaison
     * @param {Object} data - Données à envoyer
     * @param {Object} options - Options supplémentaires
     * @returns {Promise} Réponse de l'API
     */
    async patch(endpoint, data, options = {}) {
        return this.request('PATCH', endpoint, data, options);
    }

    /**
     * Requête DELETE
     * @param {string} endpoint - Point de terminaison
     * @param {Object} options - Options supplémentaires
     * @returns {Promise} Réponse de l'API
     */
    async delete(endpoint, options = {}) {
        return this.request('DELETE', endpoint, null, options);
    }

    /**
     * Upload de fichier
     * @param {string} endpoint - Point de terminaison
     * @param {File} file - Fichier à uploader
     * @param {Object} additionalData - Données supplémentaires
     * @returns {Promise} Réponse de l'API
     */
    async uploadFile(endpoint, file, additionalData = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        // Ajouter les données supplémentaires
        Object.keys(additionalData).forEach(key => {
            formData.append(key, additionalData[key]);
        });

        return this.post(endpoint, formData);
    }

    /**
     * Définit le token d'authentification
     * @param {string} token - Token d'authentification
     */
    setAuthToken(token) {
        this.token = token;
        if (token) {
            this.defaultHeaders['Authorization'] = `Bearer ${token}`;
            localStorage.setItem('operator_token', token);
        } else {
            delete this.defaultHeaders['Authorization'];
            localStorage.removeItem('operator_token');
        }
    }

    /**
     * Gère les erreurs d'authentification
     */
    handleAuthError() {
        // Supprimer le token
        this.setAuthToken(null);
        
        // Rediriger vers la page de connexion
        if (window.location.pathname !== '/operator/login.html') {
            window.location.href = '/operator/login.html';
        }
    }

    /**
     * Vérifie si l'utilisateur est authentifié
     * @returns {boolean} True si authentifié
     */
    isAuthenticated() {
        return !!this.token;
    }

    /**
     * Méthodes spécifiques pour l'opérateur
     */

    // Authentification
    async login(credentials) {
        const response = await this.post('/operator/auth/login', credentials);
        if (response.token) {
            this.setAuthToken(response.token);
        }
        return response;
    }

    async logout() {
        try {
            await this.post('/operator/auth/logout');
        } catch (error) {
            console.error('Erreur lors de la déconnexion:', error);
        } finally {
            this.setAuthToken(null);
        }
    }

    // Dashboard
    async getDashboardStats() {
        return this.get('/operator/dashboard/stats');
    }

    async getDashboardRevenue(period = '7d') {
        return this.get(`/operator/dashboard/revenue?period=${period}`);
    }

    // Lieux
    async getLocations() {
        return this.get('/operator/locations');
    }

    async createLocation(locationData) {
        return this.post('/operator/locations', locationData);
    }

    async updateLocation(id, locationData) {
        return this.put(`/operator/locations/${id}`, locationData);
    }

    async deleteLocation(id) {
        return this.delete(`/operator/locations/${id}`);
    }

    // Arrêts
    async getStops() {
        return this.get('/operator/stops');
    }

    async createStop(stopData) {
        return this.post('/operator/stops', stopData);
    }

    async updateStop(id, stopData) {
        return this.put(`/operator/stops/${id}`, stopData);
    }

    async deleteStop(id) {
        return this.delete(`/operator/stops/${id}`);
    }

    // Bus
    async getBuses() {
        return this.get('/operator/buses');
    }

    async createBus(busData) {
        return this.post('/operator/buses', busData);
    }

    async updateBus(id, busData) {
        return this.put(`/operator/buses/${id}`, busData);
    }

    async deleteBus(id) {
        return this.delete(`/operator/buses/${id}`);
    }

    // Itinéraires
    async getRoutes() {
        return this.get('/operator/routes');
    }

    async createRoute(routeData) {
        return this.post('/operator/routes', routeData);
    }

    async updateRoute(id, routeData) {
        return this.put(`/operator/routes/${id}`, routeData);
    }

    async deleteRoute(id) {
        return this.delete(`/operator/routes/${id}`);
    }

    // Voyages
    async getTrips(filters = {}) {
        const params = new URLSearchParams(filters);
        return this.get(`/operator/trips?${params}`);
    }

    async createTrip(tripData) {
        return this.post('/operator/trips', tripData);
    }

    async updateTrip(id, tripData) {
        return this.put(`/operator/trips/${id}`, tripData);
    }

    async deleteTrip(id) {
        return this.delete(`/operator/trips/${id}`);
    }

    // Réservations
    async getBookings(filters = {}) {
        const params = new URLSearchParams(filters);
        return this.get(`/operator/bookings?${params}`);
    }

    async updateBookingStatus(id, status) {
        return this.patch(`/operator/bookings/${id}/status`, { status });
    }

    // Paiements
    async getPayments(filters = {}) {
        const params = new URLSearchParams(filters);
        return this.get(`/operator/payments?${params}`);
    }

    // Utilisateurs
    async getUsers(filters = {}) {
        const params = new URLSearchParams(filters);
        return this.get(`/operator/users?${params}`);
    }

    async createUser(userData) {
        return this.post('/operator/users', userData);
    }

    async updateUser(id, userData) {
        return this.put(`/operator/users/${id}`, userData);
    }

    async deleteUser(id) {
        return this.delete(`/operator/users/${id}`);
    }

    // Validation de tickets
    async validateTicket(ticketCode) {
        return this.post('/operator/tickets/validate', { code: ticketCode });
    }

    // Rapports
    async generateReport(reportType, parameters = {}) {
        return this.post(`/operator/reports/${reportType}`, parameters);
    }
}

// Créer une instance globale du client API
window.ApiClient = new ApiClient();
