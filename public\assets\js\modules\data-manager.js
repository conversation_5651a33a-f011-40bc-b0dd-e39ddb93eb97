/**
 * Module de gestion des données avec cache
 * Gère le chargement, la mise en cache et la synchronisation des données
 */

class DataManager {
    constructor() {
        this.cache = new Map();
        this.loadingStates = new Map();
        this.cacheExpiry = new Map();
        this.defaultCacheDuration = 5 * 60 * 1000; // 5 minutes
        this.apiClient = null;
        this.init();
    }

    init() {
        // Attendre que l'API client soit disponible
        if (window.ApiClient) {
            this.apiClient = window.ApiClient;
        } else {
            // Réessayer après un court délai
            setTimeout(() => this.init(), 100);
        }
    }

    /**
     * Charge les données pour une page spécifique
     * @param {string} pageName - Nom de la page
     */
    async loadPageData(pageName) {
        const dataLoaders = {
            dashboard: () => this.loadDashboardData(),
            locations: () => this.loadLocations(),
            stops: () => this.loadStops(),
            amenities: () => this.loadAmenities(),
            seatPlans: () => this.loadSeatPlans(),
            buses: () => this.loadBuses(),
            seats: () => this.loadSeats(),
            routes: () => this.loadRoutes(),
            trips: () => this.loadTrips(),
            pricing: () => this.loadPricing(),
            bookings: () => this.loadBookings(),
            payments: () => this.loadPayments(),
            users: () => this.loadUsers(),
            validation: () => this.loadValidationData(),
            reports: () => this.loadReportsData()
        };

        const loader = dataLoaders[pageName];
        if (loader) {
            try {
                await loader();
            } catch (error) {
                console.error(`Erreur lors du chargement des données pour ${pageName}:`, error);
            }
        }
    }

    /**
     * Obtient des données avec mise en cache
     * @param {string} key - Clé de cache
     * @param {Function} fetchFunction - Fonction pour récupérer les données
     * @param {number} cacheDuration - Durée de cache en millisecondes
     * @returns {Promise} Données mises en cache ou fraîches
     */
    async getCachedData(key, fetchFunction, cacheDuration = this.defaultCacheDuration) {
        // Vérifier si les données sont en cours de chargement
        if (this.loadingStates.get(key)) {
            return this.loadingStates.get(key);
        }

        // Vérifier le cache
        const cachedData = this.cache.get(key);
        const expiry = this.cacheExpiry.get(key);
        
        if (cachedData && expiry && Date.now() < expiry) {
            return cachedData;
        }

        // Charger les données
        const loadingPromise = this.loadData(key, fetchFunction, cacheDuration);
        this.loadingStates.set(key, loadingPromise);

        try {
            const data = await loadingPromise;
            return data;
        } finally {
            this.loadingStates.delete(key);
        }
    }

    /**
     * Charge et met en cache les données
     * @param {string} key - Clé de cache
     * @param {Function} fetchFunction - Fonction pour récupérer les données
     * @param {number} cacheDuration - Durée de cache
     * @returns {Promise} Données chargées
     */
    async loadData(key, fetchFunction, cacheDuration) {
        try {
            const data = await fetchFunction();
            
            // Mettre en cache
            this.cache.set(key, data);
            this.cacheExpiry.set(key, Date.now() + cacheDuration);
            
            return data;
        } catch (error) {
            console.error(`Erreur lors du chargement des données ${key}:`, error);
            throw error;
        }
    }

    /**
     * Charge les données du tableau de bord
     */
    async loadDashboardData() {
        if (!this.apiClient) return;

        const promises = [
            this.getCachedData('dashboard-stats', () => this.apiClient.get('/operator/dashboard/stats')),
            this.getCachedData('dashboard-revenue', () => this.apiClient.get('/operator/dashboard/revenue')),
            this.getCachedData('dashboard-alerts', () => this.apiClient.get('/operator/dashboard/alerts')),
            this.getCachedData('dashboard-trips-today', () => this.apiClient.get('/operator/dashboard/trips-today'))
        ];

        try {
            const [stats, revenue, alerts, tripsToday] = await Promise.all(promises);
            
            // Déclencher des événements pour mettre à jour l'interface
            this.dispatchDataEvent('dashboardStatsLoaded', stats);
            this.dispatchDataEvent('dashboardRevenueLoaded', revenue);
            this.dispatchDataEvent('dashboardAlertsLoaded', alerts);
            this.dispatchDataEvent('dashboardTripsLoaded', tripsToday);
            
        } catch (error) {
            console.error('Erreur lors du chargement des données du dashboard:', error);
        }
    }

    /**
     * Charge les lieux
     */
    async loadLocations() {
        if (!this.apiClient) return;

        try {
            const locations = await this.getCachedData('locations', () => 
                this.apiClient.get('/operator/locations')
            );
            this.dispatchDataEvent('locationsLoaded', locations);
        } catch (error) {
            console.error('Erreur lors du chargement des lieux:', error);
        }
    }

    /**
     * Charge les arrêts
     */
    async loadStops() {
        if (!this.apiClient) return;

        try {
            const stops = await this.getCachedData('stops', () => 
                this.apiClient.get('/operator/stops')
            );
            this.dispatchDataEvent('stopsLoaded', stops);
        } catch (error) {
            console.error('Erreur lors du chargement des arrêts:', error);
        }
    }

    /**
     * Charge les commodités
     */
    async loadAmenities() {
        if (!this.apiClient) return;

        try {
            const amenities = await this.getCachedData('amenities', () => 
                this.apiClient.get('/operator/amenities')
            );
            this.dispatchDataEvent('amenitiesLoaded', amenities);
        } catch (error) {
            console.error('Erreur lors du chargement des commodités:', error);
        }
    }

    /**
     * Charge les plans de sièges
     */
    async loadSeatPlans() {
        if (!this.apiClient) return;

        try {
            const seatPlans = await this.getCachedData('seatPlans', () => 
                this.apiClient.get('/operator/seat-plans')
            );
            this.dispatchDataEvent('seatPlansLoaded', seatPlans);
        } catch (error) {
            console.error('Erreur lors du chargement des plans de sièges:', error);
        }
    }

    /**
     * Charge les bus
     */
    async loadBuses() {
        if (!this.apiClient) return;

        try {
            const buses = await this.getCachedData('buses', () => 
                this.apiClient.get('/operator/buses')
            );
            this.dispatchDataEvent('busesLoaded', buses);
        } catch (error) {
            console.error('Erreur lors du chargement des bus:', error);
        }
    }

    /**
     * Charge les sièges
     */
    async loadSeats() {
        if (!this.apiClient) return;

        try {
            const seats = await this.getCachedData('seats', () => 
                this.apiClient.get('/operator/seats')
            );
            this.dispatchDataEvent('seatsLoaded', seats);
        } catch (error) {
            console.error('Erreur lors du chargement des sièges:', error);
        }
    }

    /**
     * Charge les itinéraires
     */
    async loadRoutes() {
        if (!this.apiClient) return;

        try {
            const routes = await this.getCachedData('routes', () => 
                this.apiClient.get('/operator/routes')
            );
            this.dispatchDataEvent('routesLoaded', routes);
        } catch (error) {
            console.error('Erreur lors du chargement des itinéraires:', error);
        }
    }

    /**
     * Charge les voyages
     */
    async loadTrips() {
        if (!this.apiClient) return;

        try {
            const trips = await this.getCachedData('trips', () => 
                this.apiClient.get('/operator/trips')
            );
            this.dispatchDataEvent('tripsLoaded', trips);
        } catch (error) {
            console.error('Erreur lors du chargement des voyages:', error);
        }
    }

    /**
     * Charge la tarification
     */
    async loadPricing() {
        if (!this.apiClient) return;

        try {
            const pricing = await this.getCachedData('pricing', () => 
                this.apiClient.get('/operator/pricing')
            );
            this.dispatchDataEvent('pricingLoaded', pricing);
        } catch (error) {
            console.error('Erreur lors du chargement de la tarification:', error);
        }
    }

    /**
     * Charge les réservations
     */
    async loadBookings() {
        if (!this.apiClient) return;

        try {
            const bookings = await this.getCachedData('bookings', () => 
                this.apiClient.get('/operator/bookings')
            );
            this.dispatchDataEvent('bookingsLoaded', bookings);
        } catch (error) {
            console.error('Erreur lors du chargement des réservations:', error);
        }
    }

    /**
     * Charge les paiements
     */
    async loadPayments() {
        if (!this.apiClient) return;

        try {
            const payments = await this.getCachedData('payments', () => 
                this.apiClient.get('/operator/payments')
            );
            this.dispatchDataEvent('paymentsLoaded', payments);
        } catch (error) {
            console.error('Erreur lors du chargement des paiements:', error);
        }
    }

    /**
     * Charge les utilisateurs
     */
    async loadUsers() {
        if (!this.apiClient) return;

        try {
            const users = await this.getCachedData('users', () => 
                this.apiClient.get('/operator/users')
            );
            this.dispatchDataEvent('usersLoaded', users);
        } catch (error) {
            console.error('Erreur lors du chargement des utilisateurs:', error);
        }
    }

    /**
     * Charge les données de validation
     */
    async loadValidationData() {
        // Les données de validation sont généralement en temps réel
        // Pas de cache pour cette section
    }

    /**
     * Charge les données de rapports
     */
    async loadReportsData() {
        // Les rapports sont générés à la demande
        // Pas de cache automatique
    }

    /**
     * Déclenche un événement de données
     * @param {string} eventName - Nom de l'événement
     * @param {*} data - Données à transmettre
     */
    dispatchDataEvent(eventName, data) {
        const event = new CustomEvent(eventName, {
            detail: data
        });
        document.dispatchEvent(event);
    }

    /**
     * Invalide le cache pour une clé donnée
     * @param {string} key - Clé de cache à invalider
     */
    invalidateCache(key) {
        this.cache.delete(key);
        this.cacheExpiry.delete(key);
    }

    /**
     * Vide tout le cache
     */
    clearCache() {
        this.cache.clear();
        this.cacheExpiry.clear();
        this.loadingStates.clear();
    }

    /**
     * Obtient les données du cache sans les recharger
     * @param {string} key - Clé de cache
     * @returns {*} Données en cache ou null
     */
    getCacheData(key) {
        const expiry = this.cacheExpiry.get(key);
        if (expiry && Date.now() < expiry) {
            return this.cache.get(key);
        }
        return null;
    }
}

// Créer une instance globale du DataManager
window.DataManager = new DataManager();
