<!-- Section Tableau de bord -->
<div id="dashboardSection" class="content-section">
    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-primary text-white">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <h3 class="mb-1" id="totalBookingsToday">0</h3>
                <p class="text-muted mb-0">Réservations aujourd'hui</p>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-success text-white">
                    <i class="fas fa-bus"></i>
                </div>
                <h3 class="mb-1" id="activeTrips">0</h3>
                <p class="text-muted mb-0">Voyages en cours</p>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-warning text-white">
                    <i class="fas fa-coins"></i>
                </div>
                <h3 class="mb-1" id="revenueToday">0 FCFA</h3>
                <p class="text-muted mb-0">Revenus aujourd'hui</p>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-info text-white">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="mb-1" id="totalPassengers">0</h3>
                <p class="text-muted mb-0">Passagers aujourd'hui</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Graphique des revenus -->
        <div class="col-lg-8 mb-4">
            <div class="chart-container">
                <h5 class="mb-3"><i class="fas fa-chart-line me-2"></i>Évolution des revenus (7 derniers jours)</h5>
                <canvas id="revenueChart" height="100"></canvas>
            </div>
        </div>

        <!-- Alertes et notifications -->
        <div class="col-lg-4 mb-4">
            <div class="chart-container">
                <h5 class="mb-3"><i class="fas fa-bell me-2"></i>Alertes</h5>
                <div id="alertsContainer">
                    <div class="text-center py-4">
                        <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                        <p class="mt-2 text-muted">Chargement...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Voyages du jour -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3"><i class="fas fa-calendar-day me-2"></i>Voyages d'aujourd'hui</h5>
                <div id="todayTripsContainer">
                    <div class="text-center py-4">
                        <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                        <p class="mt-2 text-muted">Chargement...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Script spécifique au tableau de bord
(function() {
    'use strict';

    // Variables globales pour les graphiques
    let revenueChart = null;

    // Initialisation du tableau de bord
    function initDashboard() {
        // Écouter les événements de données
        document.addEventListener('dashboardStatsLoaded', handleStatsLoaded);
        document.addEventListener('dashboardRevenueLoaded', handleRevenueLoaded);
        document.addEventListener('dashboardAlertsLoaded', handleAlertsLoaded);
        document.addEventListener('dashboardTripsLoaded', handleTripsLoaded);

        // Initialiser le graphique des revenus
        initRevenueChart();

        // Charger les données si le DataManager est disponible
        if (window.DataManager) {
            window.DataManager.loadDashboardData();
        }
    }

    // Gestion des statistiques chargées
    function handleStatsLoaded(event) {
        const stats = event.detail;
        
        // Mettre à jour les cartes de statistiques
        updateStatCard('totalBookingsToday', stats.bookingsToday || 0);
        updateStatCard('activeTrips', stats.activeTrips || 0);
        updateStatCard('revenueToday', window.Utils ? window.Utils.formatCurrency(stats.revenueToday || 0) : (stats.revenueToday || 0) + ' FCFA');
        updateStatCard('totalPassengers', stats.passengersToday || 0);
    }

    // Gestion des données de revenus chargées
    function handleRevenueLoaded(event) {
        const revenueData = event.detail;
        updateRevenueChart(revenueData);
    }

    // Gestion des alertes chargées
    function handleAlertsLoaded(event) {
        const alerts = event.detail;
        updateAlertsContainer(alerts);
    }

    // Gestion des voyages du jour chargés
    function handleTripsLoaded(event) {
        const trips = event.detail;
        updateTodayTripsContainer(trips);
    }

    // Met à jour une carte de statistique
    function updateStatCard(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            // Animation de compteur
            animateCounter(element, value);
        }
    }

    // Animation de compteur
    function animateCounter(element, targetValue) {
        const startValue = parseInt(element.textContent) || 0;
        const duration = 1000; // 1 seconde
        const startTime = Date.now();

        function updateCounter() {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Fonction d'easing
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.round(startValue + (targetValue - startValue) * easeOutQuart);
            
            if (typeof targetValue === 'string') {
                element.textContent = targetValue;
            } else {
                element.textContent = currentValue;
            }

            if (progress < 1) {
                requestAnimationFrame(updateCounter);
            }
        }

        updateCounter();
    }

    // Initialise le graphique des revenus
    function initRevenueChart() {
        const ctx = document.getElementById('revenueChart');
        if (!ctx) return;

        revenueChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Revenus (FCFA)',
                    data: [],
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return window.Utils ? window.Utils.formatCurrency(value) : value + ' FCFA';
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    // Met à jour le graphique des revenus
    function updateRevenueChart(data) {
        if (!revenueChart || !data) return;

        revenueChart.data.labels = data.labels || [];
        revenueChart.data.datasets[0].data = data.values || [];
        revenueChart.update();
    }

    // Met à jour le conteneur des alertes
    function updateAlertsContainer(alerts) {
        const container = document.getElementById('alertsContainer');
        if (!container) return;

        if (!alerts || alerts.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-2x text-success"></i>
                    <p class="mt-2 text-muted">Aucune alerte</p>
                </div>
            `;
            return;
        }

        let html = '';
        alerts.forEach(alert => {
            const iconClass = getAlertIcon(alert.type);
            const timeAgo = window.Utils ? window.Utils.formatDate(alert.created_at, 'time') : alert.created_at;
            
            html += `
                <div class="alert-item">
                    <div class="d-flex align-items-start">
                        <i class="${iconClass} me-2 mt-1"></i>
                        <div class="flex-grow-1">
                            <small class="text-muted">${timeAgo}</small>
                            <p class="mb-0 small">${alert.message}</p>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    // Met à jour le conteneur des voyages du jour
    function updateTodayTripsContainer(trips) {
        const container = document.getElementById('todayTripsContainer');
        if (!container) return;

        if (!trips || trips.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-calendar-times fa-2x text-muted"></i>
                    <p class="mt-2 text-muted">Aucun voyage programmé aujourd'hui</p>
                </div>
            `;
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-hover">';
        html += `
            <thead>
                <tr>
                    <th>Itinéraire</th>
                    <th>Heure de départ</th>
                    <th>Bus</th>
                    <th>Réservations</th>
                    <th>Statut</th>
                </tr>
            </thead>
            <tbody>
        `;

        trips.forEach(trip => {
            const departureTime = window.Utils ? window.Utils.formatTime(trip.departure_time) : trip.departure_time;
            const statusBadge = window.UIComponents ? window.UIComponents.createStatusBadge(trip.status) : trip.status;
            
            html += `
                <tr>
                    <td>
                        <strong>${trip.route_name}</strong><br>
                        <small class="text-muted">${trip.departure_location} → ${trip.arrival_location}</small>
                    </td>
                    <td>${departureTime}</td>
                    <td>${trip.bus_number}</td>
                    <td>
                        <span class="badge bg-secondary">${trip.bookings_count}/${trip.total_seats}</span>
                    </td>
                    <td>${statusBadge}</td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
        container.innerHTML = html;
    }

    // Obtient l'icône pour un type d'alerte
    function getAlertIcon(type) {
        const icons = {
            warning: 'fas fa-exclamation-triangle text-warning',
            error: 'fas fa-exclamation-circle text-danger',
            info: 'fas fa-info-circle text-info',
            success: 'fas fa-check-circle text-success'
        };
        return icons[type] || icons.info;
    }

    // Actualiser les données du tableau de bord
    window.refreshDashboard = function() {
        if (window.DataManager) {
            // Invalider le cache et recharger
            window.DataManager.invalidateCache('dashboard-stats');
            window.DataManager.invalidateCache('dashboard-revenue');
            window.DataManager.invalidateCache('dashboard-alerts');
            window.DataManager.invalidateCache('dashboard-trips-today');
            window.DataManager.loadDashboardData();
        }
    };

    // Initialiser quand le DOM est prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initDashboard);
    } else {
        initDashboard();
    }

})();
</script>
